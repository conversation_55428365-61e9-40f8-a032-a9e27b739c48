package fr.enedis.i2r.infra;

import static java.util.stream.Collectors.toMap;

import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Stream;


public enum IdentiteDisponible {
    IDENTITE_YOHANN("0625DB00000600", "475000DE007860000010000001000222"),
    IDENTITE_VIANNEY("0225DB00000225", "475000DE007860000010000001000354"),
    IDENTITE_ALLAN("0625DB00000500", "475000DE007860000010000001000111"),
    IDENTITE_ZAID("6625DB00000650", "475000DE007860000010000001000333"),
    IDENTITE_TEST_LOCAL("adsDeTestI2R", "475000DE007860000010000001000223");

    private static final Map<String, IdentiteDisponible> BY_ADS = Stream.of(values())
        .collect(toMap(status -> status.ads, Function.identity()));

    public final String ads;
    public final String idms;

    IdentiteDisponible(String ads, String idms) {
        this.ads = ads;
        this.idms = idms;
    }

    public static Optional<IdentiteDisponible> fromAds(String ads) {
        return Optional.ofNullable(BY_ADS.get(ads));
    }
}
