package fr.enedis.i2r.infra.rest.si;

import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ConfigurationBoitier;

public class ConfigurationBoitierIcare extends Base {
    private static final Logger logger = LoggerFactory.getLogger(ConfigurationBoitierIcare.class);
    private static final DateTimeFormatter SI_DATE_FORMATTER = DateTimeFormatter
            .ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'")
            .withZone(ZoneOffset.UTC);

    private List<Base> objects = new ArrayList<>();
    private int nb;

    public ConfigurationBoitierIcare() {
        this.setName("cfg");
        this.setClazz("Container");
    }

    public List<Base> getObjects() {
        return this.objects;
    }

    public int getNb() {
        return nb;
    }

    public void addObject(Base object) {
        this.objects.add(object);
        nb = this.objects.size();
    }

    public static ConfigurationBoitierIcare from(ConfigurationBoitier configurationBoitier) {
        logger.info("Building boitier configuration...");

        var hard = new Hard();
        hard.setIccid(configurationBoitier.iccid());
        hard.setImei(null);
        hard.setAdsid(configurationBoitier.ads());

        var device = new Device();
        device.addObject(hard);

        var logConfig = new LogConfig();
        logConfig.setLevel(configurationBoitier.logLevel().value());

        var certificate = new Certificate();
        certificate.setLastValidityCheck(SI_DATE_FORMATTER.format(configurationBoitier.lastValidityCheck()));

        var dm = new DM();
        dm.setLog(logConfig);
        dm.setNbBitsMasqueIPV4(configurationBoitier.nbBitsMasqueIpv4());
        dm.setPingFreq(configurationBoitier.pingRetryLimit());
        dm.setPingIP1(List.of(configurationBoitier.primaryDatacenterPingAddress().ip()));
        dm.setPingIP2(List.of(configurationBoitier.secondaryDatacenterPingAddress().ip()));
        dm.setState(configurationBoitier.state());

        dm.setPingTmo1(Long.valueOf(configurationBoitier.pingTimeout().toSeconds()).intValue());
        dm.setPingTmo2(Long.valueOf(configurationBoitier.pingTimeout().toSecondsPart()).intValue());
        dm.setResetTmo(0); // TODO : Quelle valeur est attendue ici ?
        dm.setResetWanTmo(0); // TODO : Quelle valeur est attendue ici ?
        dm.setRetryFreq(configurationBoitier.resetModemLimit());
        dm.setHash(configurationBoitier.configurationHash());

        var config = new ConfigurationBoitierIcare();
        config.addObject(dm);
        config.addObject(device);
        config.addObject(certificate);
        return config;
    }
}
