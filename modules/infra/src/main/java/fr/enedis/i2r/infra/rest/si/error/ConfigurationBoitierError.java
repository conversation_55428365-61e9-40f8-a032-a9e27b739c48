package fr.enedis.i2r.infra.rest.si.error;

import java.util.List;

public class ConfigurationBoitierError {
    private Origin origin;
    private List<ErrorDetail> errors;
    private Message message;

    public Origin getOrigin() {
        return origin;
    }

    public void setOrigin(Origin origin) {
        this.origin = origin;
    }

    public List<ErrorDetail> getErrors() {
        return errors;
    }

    public void setErrors(List<ErrorDetail> errors) {
        this.errors = errors;
    }

    public Message getMessage() {
        return message;
    }

    public void setMessage(Message message) {
        this.message = message;
    }
}
