package fr.enedis.i2r.infra.rest;

public interface SiHeaders {

    // Generic headers
    public final String ACCEPT = "Accept";
    public final String ACCEPT_ENCODING = "Accept-Encoding";
    public final String CONTENT_TYPE = "Content-Type";
    public final String CONTENT_ENCODING = "Content-Encoding";

    // Enedis headers
    public final String X_ERDF_HASH_HEADER = "x-erdf-hash";
    public final String X_ERDF_API_VERSION_HEADER = "x-erdf-api-version";
    public final String MA_CORRELATION_ID = "maCorrelationId";
    public final String MA_PROCESS_ID = "maProcessId";
    public final String MA_SOURCE = "maSource";
    public final String MA_DATE_EMISSION_FLUX = "maDateEmissionFlux";
    public final String MA_TYPE = "maType";
    public final String MA_VERSION = "maVersion";
    public final String MA_ID_PRM = "maIdPrm";
    public final String MA_ID_AFFAIRE = "maIdAffaire";
    public final String MA_ID_EXTERNE = "maIdExterne";
    public final String MA_ADS_BOITER = "maAdsBoitier";

    // Headers common values
    // TODO: These values are probably already defined somewhere in a library, use
    // those instead
    public final String JSON_TYPE = "application/json";
    public final String ZIP_FORMAT = "gzip";
}
