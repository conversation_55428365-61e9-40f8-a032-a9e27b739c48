package fr.enedis.i2r.infra.rest;

import static fr.enedis.i2r.infra.rest.Encoding.GZIP;

import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.UUID;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.IpAddress;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.ports.si.SiConfigurationNotifierPort;
import fr.enedis.i2r.comsi.rest.RestEndpoints;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.infra.rest.si.ConfigurationBoitierIcare;
import fr.enedis.i2r.infra.rest.si.error.ConfigurationBoitierError;
import io.javalin.openapi.HttpMethod;

public class SiClientHttpAdapter implements SiClientPort, SiConfigurationNotifierPort {

    private static final Logger logger = LoggerFactory.getLogger(SiClientHttpAdapter.class);

    private static final Integer SI_HTTP_PORT = 8443;
    private final ComSiConfiguration configuration;
    private final HttpClient httpClient;

    public SiClientHttpAdapter(ComSiConfiguration comSiConfiguration, HttpClient httpClient) {
        this.configuration = comSiConfiguration;
        this.httpClient = httpClient;
    }

    @Override
    public boolean sendConfigurationBoitier(ConfigurationBoitier config) {
        logger.info("envoi de la configuration du boitier sur le datacenter primaire");

        try {
            var datacenters = this.configuration.parseDatacenterConfiguration();

            // TODO: Prepare headers and pass them to sendConfigToDatacenter

            if (sendConfigToDatacenter(datacenters.primaryIp(), config).isSuccess()) {
                return true;
            }

            logger.warn("échec de l'envoi au datacenter primaire, tentative sur le secondaire..");

            if (sendConfigToDatacenter(datacenters.secondaryIp(), config).isSuccess()) {
                return true;
            }

            logger.warn("échec de l'envoi au datacenter secondaire");

        } catch (Exception e) {
            logger.error("Error while sending ConfigurationBoitier", e);
        }

        return false;
    }

    private HttpResponse<ConfigurationBoitierError> sendConfigToDatacenter(
            IpAddress datacenterIp,
            ConfigurationBoitier config) {

        ConfigurationBoitierIcare configIcare = ConfigurationBoitierIcare.from(config);
        var pathParams = new HashMap<String, String>();
        pathParams.put("idms", config.idms());

        HashMap<String, String> headers = new HashMap<String, String>();

        headers.put(SiHeaders.CONTENT_TYPE, SiHeaders.JSON_TYPE);
        headers.put(SiHeaders.ACCEPT, SiHeaders.JSON_TYPE);
        headers.put(SiHeaders.CONTENT_ENCODING, SiHeaders.ZIP_FORMAT);
        headers.put(SiHeaders.ACCEPT_ENCODING, SiHeaders.ZIP_FORMAT);

        headers.put(SiHeaders.X_ERDF_HASH_HEADER, config.configurationHash());
        // TODO: Investigate what 2.0 is exactly
        headers.put(SiHeaders.X_ERDF_API_VERSION_HEADER, "2.0");
        // pour l'identifiant DU MESSAGE de corrélation au format UUID
        headers.put(SiHeaders.MA_CORRELATION_ID, UUID.randomUUID().toString());
        // pour l'identifiant de processus SI (le n° de la CTD) >> Champ présent mais
        // non exploité aujourd'hui (05/11/2020)
        headers.put(SiHeaders.MA_PROCESS_ID, "CT0017");
        // pour l’identifiant de l'application qui émet l’événement (NNA)
        headers.put(SiHeaders.MA_SOURCE, "IBIS");
        // (ex : iDT vers iCoeur) pour ordonner le traitement des flux. Date au format
        // timestamp epoch
        headers.put(SiHeaders.MA_DATE_EMISSION_FLUX, String.valueOf(Instant.now().toEpochMilli()));
        // pour identifier le nom fonctionnel de l'événement
        // (ex : teleoperationPriseEnCharge)
        headers.put(SiHeaders.MA_TYPE, "ConfigurationEvent");
        // pour le suivi des versions du flux (qui peut être incrémenté pour prise en
        // compte de nouveaux besoins) identifiants fonctionnels:
        headers.put(SiHeaders.MA_VERSION, "1.0");
        headers.put(SiHeaders.MA_ADS_BOITER, config.ads());

        var request = new HttpRequest<>(HttpMethod.PUT,
                datacenterIp, SI_HTTP_PORT, RestEndpoints.SI_CONFIG,
                pathParams,
                Collections.emptyMap(),
                headers,
                configIcare,
                        GZIP,
                ConfigurationBoitierError.class);
        var response = httpClient.retryRequest(request);
        if (response.getStatus().isError() && response.getBody() != null) {
            logger.error("Error received from API: {}", response.getBody());
        }

        return response;
    }

    @Override
    public void notifyStateChange(BipStatus newStatus) {
        // TODO Cette méthode se charge de bien former la requête informant de la mise à jour du state
        throw new UnsupportedOperationException("Unimplemented method 'notifyStateChange'");
    }

    @Override
    public SiConfigurationNotifierPort getSiConfigurationNotifier() {
        // TODO Cette méthode retourne le service qui se charge des notifications
        // Que ce soit cette classe, ou une autre
        return this;
    }
}
