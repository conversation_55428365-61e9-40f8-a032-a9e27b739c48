package fr.enedis.i2r.infra.rest.si;

import java.util.ArrayList;
import java.util.List;

public class Device extends Base {

    private List<Base> objects = new ArrayList<>();
    // TODO: Init nb at 0 and increment for each added object in the list
    // for the time being we use an harcoded "7" because the SI expect this value to
    // be 7 to validate the json
    private int nb = 7;

    public Device() {
        this.setClazz("Container");
        this.setName("device");
    }

    public List<Base> getObjects() {
        return this.objects;
    }

    public int getNb() {
        return nb;
    }

    public void addObject(Base object) {
        this.objects.add(object);
        // TODO: init nb at 0 and increment it for each added object
        // nb = this.objects.size();
    }
}
