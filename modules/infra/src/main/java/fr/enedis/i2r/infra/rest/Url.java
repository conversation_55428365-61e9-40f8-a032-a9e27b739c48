package fr.enedis.i2r.infra.rest;

import java.util.List;
import java.util.Map;

import javax.validation.constraints.NotNull;

import fr.enedis.i2r.comsi.IpAddress;

public class Url {

    public static String build(final IpAddress ipSi, final Integer port, final String endpointTemplate,
            @NotNull final Map<String, String> pathParams, @NotNull final Map<String, String> queryParams) {
        String path = endpointTemplate;

        for (Map.Entry<String, String> entry : pathParams.entrySet()) {
            path = path.replace("{" + entry.getKey() + "}", entry.getValue());
        }

        String url = String.format("https://%s:%d%s", ipSi.toString(), port, path);

        if (!queryParams.isEmpty()) {
            List<String> parameters = queryParams.entrySet()
                    .stream()
                    .map(entry -> entry.getKey() + "=" + entry.getValue())
                    .toList();

            StringBuilder queryString = new StringBuilder("?");
            queryString.append(String.join("&", parameters));
            url += queryString;
        }

        return url;
    }

}
