package fr.enedis.i2r.infra.params;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.HashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import ch.qos.logback.classic.Level;
import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.infra.params.errors.InvalidParameterException;
import fr.enedis.i2r.infra.params.errors.RequiredParameterMissingException;
import fr.enedis.i2r.infra.params.providers.ComSiParameters;
import fr.enedis.i2r.infra.params.providers.SystemParameters;
import fr.enedis.i2r.system.SystemConfiguration;
import fr.enedis.i2r.system.ports.SystemParametersPort;

public class SqliteParamsProvider implements SystemParametersPort, ComsiParametersPort {

    private static final Logger logger = LoggerFactory.getLogger(SqliteParamsProvider.class);

    private final Connection connection;

    private Configuration configuration;
    private SqliteParamsHasher hasher;

    public SqliteParamsProvider() throws SQLException {
        this(Constants.DEFAULT_DATABASE_PATH);
    }

    public SqliteParamsProvider(String dbPath) throws SQLException {
        this.connection = DriverManager.getConnection("jdbc:sqlite:" + dbPath);
        ensureTableIsCreated();
        this.configuration = fetchConfiguration();
    }

    private void ensureTableIsCreated() throws SQLException {
        var sql = "";
        sql = String.format("""
                CREATE TABLE IF NOT EXISTS %s(
                    %s text not null primary key,
                    %s text not null,
                    %s boolean
                )""",
            Constants.PARAMS_TABLE_NAME,
            Constants.PARAMS_LABEL_COLUMN_NAME,
            Constants.PARAMS_VALUE_COLUMN_NAME,
            Constants.PARAMS_UPDATED_COLUMN_NAME);

        try (var pstmt = this.connection.prepareStatement(sql)) {
            pstmt.executeUpdate();
        }

        // Il faut utiliser les prepareStatement pour remplir les variables, et non le string template pour éviter les soucis d'échappement
        sql = String.format("""
                CREATE TRIGGER IF NOT EXISTS set_updated_true
                AFTER UPDATE OF %s ON %s
                FOR EACH ROW
                WHEN OLD.last_update_utc = NEW.last_update_utc
                BEGIN
                    UPDATE %s
                    SET %s = 1
                    WHERE rowid = NEW.rowid;
                END;""",
            Constants.PARAMS_VALUE_COLUMN_NAME,
            Constants.PARAMS_TABLE_NAME,
            Constants.PARAMS_TABLE_NAME,
            Constants.PARAMS_UPDATED_COLUMN_NAME);

        try (var pstmt = this.connection.prepareStatement(sql)) {
            pstmt.executeUpdate();
        }
    }

    private Configuration fetchConfiguration() throws SQLException {
        var sql = String.format("SELECT %s, %s FROM %s", Constants.PARAMS_LABEL_COLUMN_NAME,
                Constants.PARAMS_VALUE_COLUMN_NAME, Constants.PARAMS_TABLE_NAME);

        HashMap<String, String> parameters = new HashMap<>();

        try (var stmt = this.connection.createStatement();
                var rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                parameters.put(rs.getString(Constants.PARAMS_LABEL_COLUMN_NAME),
                        rs.getString(Constants.PARAMS_VALUE_COLUMN_NAME));
            }

            return new Configuration(parameters);
        } catch (SQLException e) {
            logger.error("Récupération des paramètres dans la base SQLite", e);
            throw e;
        }
    }

    public void upsertParameter(String key, String newValue) throws SQLException {
        var sql = "INSERT INTO parameters(param_name, param_value) VALUES(?, ?) ON CONFLICT(param_name) DO UPDATE SET param_value=excluded.param_value";
        var upsertStatement = this.connection.prepareStatement(sql);

        upsertStatement.setString(1, key);
        upsertStatement.setString(2, newValue);

        upsertStatement.executeUpdate();
    }

    @Override
    public String getConfigurationHash() {
        hasher = new SqliteParamsHasher(configuration);
        return hasher.generateTableHash();
    }

    @Override
    public SystemConfiguration getSystemConfiguration() {
        return SystemParameters.getSystemConfiguration(configuration);
    }

    public ComSiConfiguration getComSiConfiguration()
            throws RequiredParameterMissingException, InvalidParameterException {
        return ComSiParameters.getComSiConfiguration(configuration);
    }

    public void refreshConfiguration() throws SQLException {
        this.configuration = fetchConfiguration();
    }

    @Override
    public void updateBipStatus(BipStatus status) {
        try {
            this.upsertParameter(ComSiParameters.BIP_STATUS.primaryKey(), Integer.toString(status.statusCode));
            this.refreshConfiguration();
        } catch (SQLException sqle) {
            logger.error("erreur lors de la mise à jour du statut du bip en base de données");
        }
    }

    @Override
    public void setLogLevel(Level logLevel) {
        try {
            this.upsertParameter(SystemParameters.LOG_LEVEL.primaryKey(), logLevel.levelStr);
            this.refreshConfiguration();
        } catch (SQLException sqle) {
            logger.error("erreur lors de la mise à jour du niveau de logs");
        }
    }

    // Changement à apporter :
    // Cette méthode doit utiliser les preparedstatement et pas le string format
    // Ensuite, pour s'assurer qu'il n'y ait pas de chevauchement possible,
    // la récupération et le reset des "updated" doit se faire dans la même transaction.
    public HashMap<String, String> fetchAndResetUpdatedParameters() {
        logger.info("Fetching updated parameters from database");
        var sql = String.format("SELECT %s, %s FROM %s AND updated = true",
                Constants.PARAMS_LABEL_COLUMN_NAME, Constants.PARAMS_VALUE_COLUMN_NAME, Constants.PARAMS_TABLE_NAME);

        HashMap<String, String> parameters = new HashMap<>();

        try (var stmt = this.connection.createStatement();
                var rs = stmt.executeQuery(sql)) {
            while (rs.next()) {
                parameters.put(rs.getString(Constants.PARAMS_LABEL_COLUMN_NAME),
                        rs.getString(Constants.PARAMS_VALUE_COLUMN_NAME));
            }
        } catch (SQLException e) {
            logger.error("Error while fetching updated parameters from database", e);
        }

        this.resetUpdatedParameters();

        return parameters;
    }

    public void resetUpdatedParameters() {
        logger.info("Reseting updated parameters on database");
        try (var stmt = this.connection.prepareStatement("UPDATE parameters SET updated = false")) {
            stmt.executeUpdate();
        } catch (SQLException e) {
            logger.error("Error while reseting updated parameters on database", e);
        }

    }

}
