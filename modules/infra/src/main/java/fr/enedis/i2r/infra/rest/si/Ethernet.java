package fr.enedis.i2r.infra.rest.si;

public class Ethernet extends Base {
    private boolean enable;
    private IPv4Address ipv4Address;
    private IPv6Address ipv6Address;
    private String macAddress;
    private Integer status;

    public Ethernet() {
        this.setClazz("Ethernet");
        this.setName("ethernet");
    }

    public boolean isEnable() {
        return enable;
    }

    public void setEnable(boolean enable) {
        this.enable = enable;
    }

    public IPv4Address getIpv4Address() {
        return ipv4Address;
    }

    public void setIpv4Address(IPv4Address ipv4Address) {
        this.ipv4Address = ipv4Address;
    }

    public IPv6Address getIpv6Address() {
        return ipv6Address;
    }

    public void setIpv6Address(IPv6Address ipv6Address) {
        this.ipv6Address = ipv6Address;
    }

    public String getMacAddress() {
        return macAddress;
    }

    public void setMacAddress(String macAddress) {
        this.macAddress = macAddress;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
