package fr.enedis.i2r.infra.rest.si;

public class Certificate extends Base {
    private String lastValidityCheck;
    private String nextValidityCheck;
    private String notAfter;
    private int ttl;

    public Certificate() {
        this.setClazz("CertificateCheck");
        this.setName("certificate");
    }

    public String getLastValidityCheck() {
        return lastValidityCheck;
    }

    public void setLastValidityCheck(String lastValidityCheck) {
        this.lastValidityCheck = lastValidityCheck;
    }

    public String getNextValidityCheck() {
        return nextValidityCheck;
    }

    public void setNextValidityCheck(String nextValidityCheck) {
        this.nextValidityCheck = nextValidityCheck;
    }

    public String getNotAfter() {
        return notAfter;
    }

    public void setNotAfter(String notAfter) {
        this.notAfter = notAfter;
    }

    public int getTtl() {
        return ttl;
    }

    public void setTtl(int ttl) {
        this.ttl = ttl;
    }
}
