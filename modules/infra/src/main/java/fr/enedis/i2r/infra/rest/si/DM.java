package fr.enedis.i2r.infra.rest.si;

import java.util.List;

public class DM extends Base {
    private List<APN> apn;
    private int gzipLevel;
    private int httpTmo;
    private int linkType;
    private LogConfig log;
    private int nbBitsMasqueIPV4;
    private int nbBitsMasqueIPV6;
    private String pin;
    private boolean pinEnabled;
    private int pingFreq;
    private List<String> pingIP1;
    private List<String> pingIP2;
    private int pingTmo1;
    private int pingTmo2;
    private boolean reachable;
    private int resetTmo;
    private int resetWanTmo;
    private int retryFreq;
    private Integer state;
    private int system;
    private int tcpSynRetry;
    private String validDate;
    private String version;
    private String hash;

    public DM() {
        this.setName("dm");
        this.setClazz("DM");
        this.setFlow("/db/cfg/flows/cfg");
    }

    public List<APN> getApn() {
        return apn;
    }

    public void setApn(List<APN> apn) {
        this.apn = apn;
    }

    public int getGzipLevel() {
        return gzipLevel;
    }

    public void setGzipLevel(int gzipLevel) {
        this.gzipLevel = gzipLevel;
    }

    public int getHttpTmo() {
        return httpTmo;
    }

    public void setHttpTmo(int httpTmo) {
        this.httpTmo = httpTmo;
    }

    public int getLinkType() {
        return linkType;
    }

    public void setLinkType(int linkType) {
        this.linkType = linkType;
    }

    public LogConfig getLog() {
        return log;
    }

    public void setLog(LogConfig log) {
        this.log = log;
    }

    public int getNbBitsMasqueIPV4() {
        return nbBitsMasqueIPV4;
    }

    public void setNbBitsMasqueIPV4(int nbBitsMasqueIPV4) {
        this.nbBitsMasqueIPV4 = nbBitsMasqueIPV4;
    }

    public int getNbBitsMasqueIPV6() {
        return nbBitsMasqueIPV6;
    }

    public void setNbBitsMasqueIPV6(int nbBitsMasqueIPV6) {
        this.nbBitsMasqueIPV6 = nbBitsMasqueIPV6;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public boolean isPinEnabled() {
        return pinEnabled;
    }

    public void setPinEnabled(boolean pinEnabled) {
        this.pinEnabled = pinEnabled;
    }

    public int getPingFreq() {
        return pingFreq;
    }

    public void setPingFreq(int pingFreq) {
        this.pingFreq = pingFreq;
    }

    public List<String> getPingIP1() {
        return pingIP1;
    }

    public void setPingIP1(List<String> pingIP1) {
        this.pingIP1 = pingIP1;
    }

    public List<String> getPingIP2() {
        return pingIP2;
    }

    public void setPingIP2(List<String> pingIP2) {
        this.pingIP2 = pingIP2;
    }

    public int getPingTmo1() {
        return pingTmo1;
    }

    public void setPingTmo1(int pingTmo1) {
        this.pingTmo1 = pingTmo1;
    }

    public int getPingTmo2() {
        return pingTmo2;
    }

    public void setPingTmo2(int pingTmo2) {
        this.pingTmo2 = pingTmo2;
    }

    public boolean isReachable() {
        return reachable;
    }

    public void setReachable(boolean reachable) {
        this.reachable = reachable;
    }

    public int getResetTmo() {
        return resetTmo;
    }

    public void setResetTmo(int resetTmo) {
        this.resetTmo = resetTmo;
    }

    public int getResetWanTmo() {
        return resetWanTmo;
    }

    public void setResetWanTmo(int resetWanTmo) {
        this.resetWanTmo = resetWanTmo;
    }

    public int getRetryFreq() {
        return retryFreq;
    }

    public void setRetryFreq(int retryFreq) {
        this.retryFreq = retryFreq;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public int getSystem() {
        return system;
    }

    public void setSystem(int system) {
        this.system = system;
    }

    public int getTcpSynRetry() {
        return tcpSynRetry;
    }

    public void setTcpSynRetry(int tcpSynRetry) {
        this.tcpSynRetry = tcpSynRetry;
    }

    public String getValidDate() {
        return validDate;
    }

    public void setValidDate(String validDate) {
        this.validDate = validDate;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getHash() {
        return hash;
    }

    public void setHash(String hash) {
        this.hash = hash;
    }
}
