package fr.enedis.i2r.infra.rest;

import java.util.Map;

import fr.enedis.i2r.comsi.IpAddress;
import io.javalin.openapi.HttpMethod;

public record HttpRequest<RESPONSE>(
        HttpMethod httpMethod,
        IpAddress ip,
        Integer port,
        String endpoint,
        Map<String, String> pathParams,
        Map<String, String> queryParams,
        Map<String, String> headers,
        Object payload,
        Encoding encoding,
        Class<RESPONSE> responseType) {

}
