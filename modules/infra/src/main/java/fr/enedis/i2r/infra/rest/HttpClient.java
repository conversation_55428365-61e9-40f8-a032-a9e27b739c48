package fr.enedis.i2r.infra.rest;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;
import java.util.zip.GZIPOutputStream;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;

import io.javalin.http.HttpStatus;

public class HttpClient {

    private static final Logger logger = LoggerFactory.getLogger(HttpClient.class);
    private SSLContext sslContext;

    public HttpClient(SSLContext sslContext) {
        this.sslContext = sslContext;
    }

    public <RESPONSE> HttpResponse<RESPONSE> retryRequest(HttpRequest<RESPONSE> httpRequest) {
        return retryRequest(httpRequest, RetryConfig.defaultConfig());
    }

    public <RESPONSE> HttpResponse<RESPONSE> retryRequest(
            HttpRequest<RESPONSE> httpRequest,
            RetryConfig retryConfig) {
        var response = new HttpResponse<RESPONSE>();

        for (int attempts = 1; attempts <= retryConfig.maximumAttempts(); attempts++) {
            logger.info("Attempt {}/{} for {}", attempts, retryConfig.maximumAttempts(), httpRequest.endpoint());

            response = sendRequest(httpRequest);
            if (response.getStatus().isSuccess()) {
                logger.info("Successfully executed request {}", httpRequest.endpoint());
                return response;
            }

            try {
                TimeUnit.SECONDS.sleep(retryConfig.delay().getSeconds());
            } catch (InterruptedException e) {
                logger.warn("Interrupted while waiting for retry, last failed response was returned", e);
                return response;
            }
        }

        logger.info("Failed request {}", httpRequest.endpoint());
        return response;
    }

    /**
     * Envoie la requête contenue dans le paramètre.
     *<p>
     * Nous n'utilisons pas ici l'API `java.net.http` à cause de la compression
     * GZIP.
     *<p>
     * Afin de compresser la requête, il faut passer par GzipOutputStream. Et si
     * nous voulons utiliser les API récentes du JDK, il faut forcément stocker le
     * contenu gzippé dans une variable, avant de l'envoyer. Le souci : c'est que
     * cela fait de l'utilisation mémoire inutile.
     * Utiliser directement HttpUrlConnection nous permet de relayer la sortie de
     * GzipOutputStream directement vers le flux HTTP, sans le stocker chez nous.
     *<p>
     * Les piped(input/output)stream ne sont pas envisageables :
     * https://stackoverflow.com/questions/5778658/how-do-i-convert-an-outputstream-to-an-inputstream
     */
    public <RESPONSE> HttpResponse<RESPONSE> sendRequest(HttpRequest<RESPONSE> httpRequest) {
        logger.debug("Calling REST endpoint: {}", httpRequest.endpoint());
        HttpResponse<RESPONSE> httpResponse = new HttpResponse<>();

        try {
            String url = Url.build(httpRequest.ip(), httpRequest.port(), httpRequest.endpoint(),
                    httpRequest.pathParams(), httpRequest.queryParams());
            logger.debug("URL to be reached: {}", url);
            JsonMapper jsonMapper = JsonMapper.builder().configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY,
                    true).build();
            String jsonPayload = jsonMapper.writeValueAsString(httpRequest.payload());

            logger.debug("JSON Payload: {}", jsonPayload);

            HttpsURLConnection conn = prepareHttpsURLConnection(httpRequest, url);
            sendPayload(conn, jsonPayload, httpRequest.encoding());
            int responseCode = conn.getResponseCode();
            httpResponse.setStatus(HttpStatus.forStatus(responseCode));
            logger.info("Response Code: {}", responseCode);

            boolean isSuccess = HttpStatus.forStatus(responseCode).isSuccess();
            StringBuilder responsePayload = new StringBuilder();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(
                    isSuccess ? conn.getInputStream() : conn.getErrorStream(), StandardCharsets.UTF_8))) {
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    responsePayload.append(responseLine.trim());
                }
            }

            String rawResponse = responsePayload.toString();
            httpResponse.setRawBody(rawResponse);
            httpResponse.setBody(null);
            if (!isSuccess) {
                httpResponse.setBody(jsonMapper.readValue(rawResponse, httpRequest.responseType()));
            }

            logger.info("Response {}", rawResponse);

            conn.disconnect();
        } catch (Exception e) {
            logger.error("Error while calling endpoint: {}", httpRequest.endpoint(), e);
        }
        return httpResponse;
    }

    private <RESPONSE> HttpsURLConnection prepareHttpsURLConnection(HttpRequest<RESPONSE> httpRequest, String url)
            throws URISyntaxException, IOException {
        URI uri = new URI(url);
        HttpsURLConnection conn = (HttpsURLConnection) uri.toURL().openConnection();

        conn.setRequestMethod(httpRequest.httpMethod().name());
        // Use our SSL Context
        conn.setSSLSocketFactory(sslContext.getSocketFactory());

        // Set headers
        httpRequest.headers().forEach(conn::setRequestProperty);

        conn.setDoOutput(true);

        return conn;
    }

    private void sendPayload(HttpsURLConnection connection, String jsonPayload, Encoding encoding) throws IOException {
        switch (encoding) {
            case GZIP: {
                connection.setRequestProperty("Content-Encoding", SiHeaders.ZIP_FORMAT);
                connection.setRequestProperty("Accept-Encoding", SiHeaders.ZIP_FORMAT);
                try (OutputStream os = new GZIPOutputStream(connection.getOutputStream())) {
                    os.write(jsonPayload.getBytes());
                    os.flush();
                }
            }
            case NONE: {
                try (OutputStream os = connection.getOutputStream()) {
                    os.write(jsonPayload.getBytes(StandardCharsets.UTF_8));
                    os.flush();
                }
            }
        }
    }
}
