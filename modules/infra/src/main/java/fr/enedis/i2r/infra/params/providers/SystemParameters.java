package fr.enedis.i2r.infra.params.providers;

import ch.qos.logback.classic.Level;
import fr.enedis.i2r.infra.params.Configuration;
import fr.enedis.i2r.infra.params.ParameterWithDefault;
import fr.enedis.i2r.system.SystemConfiguration;

import java.util.Arrays;
import java.util.List;

public class SystemParameters {
    public static final ParameterWithDefault<Level> LOG_LEVEL = new ParameterWithDefault<>(
        "i2r.system.log.level",
        (valeur) -> Level.toLevel(valeur, SystemConfiguration.DEFAULT_LOG_LEVEL),
        SystemConfiguration.DEFAULT_LOG_LEVEL);

    public static final ParameterWithDefault<List<String>> SECONDARY_SERVICES = new ParameterWithDefault<>(
        "i2r.system.secondary_services",
        (valeur) -> Arrays.asList(valeur.split(",")),
        SystemConfiguration.DEFAULT_SECONDARY_SERVICES);

    public static SystemConfiguration getSystemConfiguration(Configuration configuration) {
        try {
            return new SystemConfiguration(
                configuration.parseOrDefault(LOG_LEVEL),
                configuration.parseOrDefault(SECONDARY_SERVICES)
            );
        } catch (Exception e) {
            throw new IllegalArgumentException("Rassemblement de la configuration de System", e);
        }
    }
}
