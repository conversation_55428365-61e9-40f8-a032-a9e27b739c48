package fr.enedis.i2r.infra.params;

import static java.util.Collections.emptyList;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardWatchEventKinds;
import java.nio.file.WatchEvent;
import java.nio.file.WatchKey;
import java.nio.file.WatchService;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;

public class DbUpdateWatcherAdapter implements DatabaseUpdateWatcherPort {

    private static final Logger logger = LoggerFactory.getLogger(DbUpdateWatcherAdapter.class);
    private final WatchService watchService;
    private final String databasePath;
    private SqliteParamsProvider paramsProvider;

    public DbUpdateWatcherAdapter(SqliteParamsProvider paramsProvider, String databasePath) throws IOException {
        this.paramsProvider = paramsProvider;
        this.databasePath = databasePath;
        this.watchService = FileSystems.getDefault().newWatchService();
    }

    @Override
    public List<ConfigurationValue> waitForUpdates()  {
        try {
            this.waitForFileChange();
            var params = this.paramsProvider.fetchAndResetUpdatedParameters();

            // TODO : faire le mapping entre les params et leurs objets métiers
            return emptyList();
        } catch (Exception e) {
            logger.error("Error while watching the params db file", e);
        }

        return emptyList();
    }

    private void waitForFileChange() throws IOException, InterruptedException {
        logger.info("Starting to watch params db file: {}", this.databasePath);

        Path dbFile = Paths.get(this.databasePath);
        Path dbDir = dbFile.getParent();
        dbDir.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY);

        while (true) {
            WatchKey key = watchService.take();
            for (WatchEvent<?> event : key.pollEvents()) {
                WatchEvent.Kind<?> kind = event.kind();
                Path changed = (Path) event.context();

                if (kind == StandardWatchEventKinds.ENTRY_MODIFY &&
                        changed.equals(dbFile.getFileName())) {
                    logger.info("Change detected on params db file");
                    return;
                }
            }
            key.reset();
        }
    }
}
