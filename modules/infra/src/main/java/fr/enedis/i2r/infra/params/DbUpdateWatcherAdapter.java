package fr.enedis.i2r.infra.params;

import static java.util.Collections.emptyList;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardWatchEventKinds;
import java.nio.file.WatchEvent;
import java.nio.file.WatchKey;
import java.nio.file.WatchService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.params.BipParameter;
import fr.enedis.i2r.comsi.params.ConfigurationValue;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;

public class DbUpdateWatcherAdapter implements DatabaseUpdateWatcherPort {

    private static final Logger logger = LoggerFactory.getLogger(DbUpdateWatcherAdapter.class);
    private final WatchService watchService;
    private final String databasePath;
    private SqliteParamsProvider paramsProvider;

    public DbUpdateWatcherAdapter(SqliteParamsProvider paramsProvider, String databasePath) throws IOException {
        this.paramsProvider = paramsProvider;
        this.databasePath = databasePath;
        this.watchService = FileSystems.getDefault().newWatchService();
    }

    @Override
    public List<ConfigurationValue> waitForUpdates()  {
        try {
            this.waitForFileChange();
            var params = this.paramsProvider.fetchAndResetUpdatedParameters();
            return mapParametersToConfigurationValues(params);
        } catch (Exception e) {
            logger.error("Error while watching the params db file", e);
        }

        return emptyList();
    }

    public List<ConfigurationValue> mapParametersToConfigurationValues(HashMap<String, String> params) {
        logger.info("Mapping parameters to configuration values...");
        List<ConfigurationValue> configurationValues = new ArrayList<>();

        for (var entry : params.entrySet()) {
            String parameterKey = entry.getKey();
            String parameterValue = entry.getValue();

            BipParameter bipParameter = null;
            for (BipParameter param : BipParameter.values()) {
                if (param.parameterKey.equals(parameterKey)) {
                    bipParameter = param;
                    break;
                }
            }

            if (bipParameter != null) {
                logger.debug("Parameter match found and mapped: {}", parameterKey);
                configurationValues.add(new ConfigurationValue(bipParameter, parameterValue));
            } else {
                logger.error("Error while mapping unknown parameter key: {}", parameterKey);
            }
        }

        return configurationValues;
    }

    private void waitForFileChange() throws IOException, InterruptedException {
        logger.info("Starting to watch params db file: {}", this.databasePath);

        Path dbFile = Paths.get(this.databasePath);
        Path dbDir = dbFile.getParent();
        dbDir.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY);

        while (true) {
            WatchKey key = watchService.take();
            for (WatchEvent<?> event : key.pollEvents()) {
                WatchEvent.Kind<?> kind = event.kind();
                Path changed = (Path) event.context();

                if (kind == StandardWatchEventKinds.ENTRY_MODIFY &&
                        changed.equals(dbFile.getFileName())) {
                    logger.info("Change detected on params db file");
                    return;
                }
            }
            key.reset();
        }
    }
}
