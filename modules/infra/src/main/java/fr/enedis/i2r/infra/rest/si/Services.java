package fr.enedis.i2r.infra.rest.si;

import java.util.Map;

public class Services extends Base {
    private Service callback;
    private Service log;
    private Service monitoring;
    private Service ssh;
    private Service supervision;
    private Service time;
    private Service upgrade;
    private Service web;
    private Map<String, Service> additionalServices;

    public Services() {
        this.setClazz("Container");
        this.setName("services");
    }

    public Service getCallback() {
        return callback;
    }

    public void setCallback(Service callback) {
        this.callback = callback;
    }

    public Service getLog() {
        return log;
    }

    public void setLog(Service log) {
        this.log = log;
    }

    public Service getMonitoring() {
        return monitoring;
    }

    public void setMonitoring(Service monitoring) {
        this.monitoring = monitoring;
    }

    public Service getSsh() {
        return ssh;
    }

    public void setSsh(Service ssh) {
        this.ssh = ssh;
    }

    public Service getSupervision() {
        return supervision;
    }

    public void setSupervision(Service supervision) {
        this.supervision = supervision;
    }

    public Service getTime() {
        return time;
    }

    public void setTime(Service time) {
        this.time = time;
    }

    public Service getUpgrade() {
        return upgrade;
    }

    public void setUpgrade(Service upgrade) {
        this.upgrade = upgrade;
    }

    public Service getWeb() {
        return web;
    }

    public void setWeb(Service web) {
        this.web = web;
    }

    public Map<String, Service> getAdditionalServices() {
        return additionalServices;
    }

    public void setAdditionalServices(Map<String, Service> additionalServices) {
        this.additionalServices = additionalServices;
    }
}
