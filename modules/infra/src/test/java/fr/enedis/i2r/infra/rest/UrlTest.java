package fr.enedis.i2r.infra.rest;

import static java.util.Collections.emptyMap;
import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.Map;

import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.IpAddress;

class UrlTest {

    @Test
    void an_url_with_no_parameters_is_not_modified() {
        String url = Url.build(new IpAddress("127.0.0.1"), 8443, "/ping", emptyMap(), emptyMap());
        assertEquals("https://127.0.0.1:8443/ping", url);
    }

    @Test
    void the_path_parameter_are_injected_in_the_url() {
        String url = Url.build(new IpAddress("127.0.0.1"), 8443, "/ping/{var}/pong", Map.of("var", "value"),
                emptyMap());
        assertEquals("https://127.0.0.1:8443/ping/value/pong", url);
    }

    @Test
    void the_query_parameters_are_added_to_the_url() {
        String url = Url.build(new IpAddress("127.0.0.1"), 8443,
                "/ping", emptyMap(), Map.of("foo", "value", "bar", "42"));
        assertThat(url).isIn("https://127.0.0.1:8443/ping?foo=value&bar=42",
                "https://127.0.0.1:8443/ping?bar=42&foo=value");
    }
}
