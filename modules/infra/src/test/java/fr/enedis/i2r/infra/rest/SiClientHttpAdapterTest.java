package fr.enedis.i2r.infra.rest;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.time.Instant;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;

import fr.enedis.i2r.comsi.ConfigurationBoitier;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.infra.rest.si.ConfigurationBoitierIcare;
import fr.enedis.i2r.infra.rest.si.error.ConfigurationBoitierError;
import io.javalin.http.HttpStatus;

public class SiClientHttpAdapterTest {
    HttpClient httpClient;
    BoardManagerPort boardManagerPort;

    @BeforeEach
    void setup() {
        httpClient = mock(HttpClient.class);
        boardManagerPort = mock(BoardManagerPort.class);
    }

    @Test
    void le_client_envoie_la_configuration_boitier_au_datacenter_primaire() {
        CustomComSiConfiguration conf = new CustomComSiConfiguration();

        var successfulResponse = new HttpResponse<ConfigurationBoitierError>();
        successfulResponse.setStatus(HttpStatus.OK);

        @SuppressWarnings("unchecked")
        ArgumentCaptor<HttpRequest<ConfigurationBoitierError>> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenReturn(successfulResponse);

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf.build(), httpClient);
        siClient.sendConfigurationBoitier(
                ConfigurationBoitier.from(conf.build(), Instant.now(), "HASH", "ADS", "IDMS", "ICCID"));

        HttpRequest<ConfigurationBoitierError> requeteEnvoyee = captor.getValue();
        assertThat(requeteEnvoyee.ip()).isEqualTo(conf.ipPacy);
    }

    @Test
    void la_configuration_est_envoyee_au_datacenter_secondaire_lorsque_l_envoi_echoue_sur_le_primaire() {
        CustomComSiConfiguration conf = new CustomComSiConfiguration();

        var notFoundResponse = new HttpResponse<ConfigurationBoitierError>();
        notFoundResponse.setStatus(HttpStatus.BAD_REQUEST);

        @SuppressWarnings("unchecked")
        ArgumentCaptor<HttpRequest<ConfigurationBoitierError>> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenReturn(notFoundResponse);

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf.build(), httpClient);
        siClient.sendConfigurationBoitier(
                ConfigurationBoitier.from(conf.build(), Instant.now(), "HASH", "ADS", "IDMS", "ICCID"));

        List<HttpRequest<ConfigurationBoitierError>> requetesEnvoyees = captor.getAllValues();
        assertThat(requetesEnvoyees).hasSize(2);
        assertThat(requetesEnvoyees.get(0).ip()).isEqualTo(conf.ipPacy);
        assertThat(requetesEnvoyees.get(1).ip()).isEqualTo(conf.ipNoe);
    }

    @Test
    void la_configuration_boitier_est_correctement_serialisee_pour_icare() {
        var conf = new CustomComSiConfiguration().build();

        var now = Instant.now();
        var confBoitier = ConfigurationBoitier.from(conf, now, "HASH", "ADS", "IDMS", "ICCID");

        var successfulResponse = new HttpResponse<ConfigurationBoitierError>();
        successfulResponse.setStatus(HttpStatus.OK);

        @SuppressWarnings("unchecked")
        ArgumentCaptor<HttpRequest<ConfigurationBoitierError>> captor = ArgumentCaptor.forClass(HttpRequest.class);

        when(httpClient.retryRequest(captor.capture())).thenReturn(successfulResponse);

        SiClientHttpAdapter siClient = new SiClientHttpAdapter(conf, httpClient);
        siClient.sendConfigurationBoitier(confBoitier);

        HttpRequest<ConfigurationBoitierError> requeteEnvoyee = captor.getValue();
        assertThat(requeteEnvoyee.ip()).isEqualTo(conf.parseDatacenterConfiguration().primaryIp());
        assertThat(requeteEnvoyee.payload()).isInstanceOf(ConfigurationBoitierIcare.class);

        ConfigurationBoitierIcare sentConfiguration = (ConfigurationBoitierIcare) requeteEnvoyee.payload();
        assertThat(sentConfiguration.getNb()).isEqualTo(3);
        // TODO: tester plus de champs
    }

}
