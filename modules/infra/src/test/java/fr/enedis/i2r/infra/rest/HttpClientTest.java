package fr.enedis.i2r.infra.rest;

import static java.util.Collections.emptyMap;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

import java.time.Duration;

import javax.net.ssl.SSLContext;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import fr.enedis.i2r.comsi.IpAddress;
import io.javalin.http.HttpStatus;
import io.javalin.openapi.HttpMethod;

class HttpClientTest {

    HttpClient client;
    HttpClient spyClient;

    HttpRequest<String> request = new HttpRequest<>(HttpMethod.GET, new IpAddress("127.0.0.1"), 8443, "ping",
            emptyMap(), emptyMap(), emptyMap(), "", Encoding.NONE,
            String.class);

    @BeforeEach
    void setup() throws Exception {
        client = new HttpClient(SSLContext.getDefault());
        spyClient = Mockito.spy(client);
    }

    @Test
    void the_sendRequestWithRetry_should_call_sendRequest_one_time_when_sendRequest_is_successful() {
        HttpResponse<String> ret = new HttpResponse<>();
        ret.setStatus(HttpStatus.OK);
        doReturn(ret).when(spyClient).sendRequest(any());

        var result = spyClient.retryRequest(request);

        assertEquals(200, result.getStatus().getCode());
        verify(spyClient, times(1)).sendRequest(any());
    }

    @Test
    void the_sendRequestWithRetry_should_call_sendRequest_3_time_when_sendRequest_failed() {
        HttpResponse<String> ret = new HttpResponse<>();
        ret.setStatus(HttpStatus.NOT_FOUND);
        doReturn(ret).when(spyClient).sendRequest(any());

        var result = spyClient.retryRequest(request, new RetryConfig(3, Duration.ZERO));

        assertEquals(404, result.getStatus().getCode());
        verify(spyClient, times(3)).sendRequest(any());
    }
}
