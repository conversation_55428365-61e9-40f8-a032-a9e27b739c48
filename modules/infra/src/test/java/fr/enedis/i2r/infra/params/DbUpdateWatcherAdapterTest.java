package fr.enedis.i2r.infra.params;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.WatchService;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.ComSI;
import fr.enedis.i2r.comsi.params.BipParameter;
import fr.enedis.i2r.comsi.params.ConfigurationValue;

class DbUpdateWatcherAdapterTest {

    private Path tempDir;
    private Path watchedFile;
    private WatchService watchService;
    private DbUpdateWatcherAdapter watcher;
    private ComSI mockComSI;
    private ExecutorService executor;
    private SqliteParamsProvider paramsProvider;

    @BeforeEach
    void setup() throws IOException, SQLException {
        tempDir = Files.createTempDirectory("watch_test");
        watchedFile = tempDir.resolve("params.db");
        Files.createFile(watchedFile);

        paramsProvider = new SqliteParamsProvider(watchedFile.toString());

        watchService = FileSystems.getDefault().newWatchService();
        watcher = new DbUpdateWatcherAdapter(paramsProvider, watchedFile.toString());

        mockComSI = mock(ComSI.class);
        executor = Executors.newSingleThreadExecutor();
    }

    @AfterEach
    void teardown() throws IOException {
        executor.shutdownNow();
        watchService.close();
        Files.deleteIfExists(watchedFile);
        Files.deleteIfExists(tempDir);
    }
    // --------
    // Avec le refacto, le test n'est plus vraiment architecturé de la même manière
    // Je vais le corriger avec mes tests
    // --------
    //
    // @Test
    // void notifie_changement_bdd_si_fichier_modifie() throws Exception {
    //     CountDownLatch latch = new CountDownLatch(1);

    //     // Stub ComSI to count down when called
    //     doAnswer(invocation -> {
    //         latch.countDown();
    //         return null;
    //     }).when(mockComSI).notifyDbChange();

    //     // Start the watcher in a separate thread
    //     executor.submit(() -> watcher.watchFile(watchedFile.toString(), mockComSI));

    //     // Wait for the watcher to register
    //     TimeUnit.MILLISECONDS.sleep(200);
    //     tempDir.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY);

    //     // Trigger file modification
    //     Files.writeString(watchedFile, "test");

    //     // Assert notifyDbChange was triggered
    //     boolean success = latch.await(3, TimeUnit.SECONDS);
    //     Assertions.assertTrue(success, "notifyDbChange should have been called");

    //     verify(mockComSI, atLeastOnce()).notifyDbChange();
    // }

    @Test
    void map_correctement_lesparametres_connus() throws Exception {
        SqliteParamsProvider mockParamsProvider = mock(SqliteParamsProvider.class);
        HashMap<String, String> params = new HashMap<>();
        params.put("i2r.bip.status", "2");

        when(mockParamsProvider.fetchAndResetUpdatedParameters()).thenReturn(params);

        DbUpdateWatcherAdapter adapter = new DbUpdateWatcherAdapter(mockParamsProvider, watchedFile.toString());

        List<ConfigurationValue> result = adapter.mapParametersToConfigurationValues(params);

        assertThat(result).hasSize(1);
        ConfigurationValue configValue = result.get(0);
        assertThat(configValue.parameter()).isEqualTo(BipParameter.BipState);
        assertThat(configValue.value()).isEqualTo("2");
    }

    @Test
    void ignore_les_parametres_inconnus() throws Exception {
        SqliteParamsProvider mockParamsProvider = mock(SqliteParamsProvider.class);
        HashMap<String, String> params = new HashMap<>();
        params.put("test.unknown", "value");
        params.put("i2r.bip.status", "1");

        when(mockParamsProvider.fetchAndResetUpdatedParameters()).thenReturn(params);

        DbUpdateWatcherAdapter adapter = new DbUpdateWatcherAdapter(mockParamsProvider, watchedFile.toString());

        List<ConfigurationValue> result = adapter.mapParametersToConfigurationValues(params);

        assertThat(result).hasSize(1); // Only the known parameter should be mapped
        ConfigurationValue configValue = result.get(0);
        assertThat(configValue.parameter()).isEqualTo(BipParameter.BipState);
        assertThat(configValue.value()).isEqualTo("1");
    }
}
