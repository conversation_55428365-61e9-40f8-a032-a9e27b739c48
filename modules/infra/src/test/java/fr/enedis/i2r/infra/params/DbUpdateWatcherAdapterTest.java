package fr.enedis.i2r.infra.params;

import static org.mockito.Mockito.mock;

import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.WatchService;
import java.sql.SQLException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;

import fr.enedis.i2r.comsi.ComSI;

class DbUpdateWatcherAdapterTest {

    private Path tempDir;
    private Path watchedFile;
    private WatchService watchService;
    private DbUpdateWatcherAdapter watcher;
    private ComSI mockComSI;
    private ExecutorService executor;
    private SqliteParamsProvider paramsProvider;

    @BeforeEach
    void setup() throws IOException, SQLException {
        tempDir = Files.createTempDirectory("watch_test");
        watchedFile = tempDir.resolve("params.db");
        Files.createFile(watchedFile);

        paramsProvider = new SqliteParamsProvider(watchedFile.toString());

        watchService = FileSystems.getDefault().newWatchService();
        watcher = new DbUpdateWatcherAdapter(paramsProvider, watchedFile.toString());

        mockComSI = mock(ComSI.class);
        executor = Executors.newSingleThreadExecutor();
    }

    @AfterEach
    void teardown() throws IOException {
        executor.shutdownNow();
        watchService.close();
        Files.deleteIfExists(watchedFile);
        Files.deleteIfExists(tempDir);
    }
    // --------
    // Avec le refacto, le test n'est plus vraiment architecturé de la même manière
    // Je vais le corriger avec mes tests
    // --------
    //
    // @Test
    // void notifie_changement_bdd_si_fichier_modifie() throws Exception {
    //     CountDownLatch latch = new CountDownLatch(1);

    //     // Stub ComSI to count down when called
    //     doAnswer(invocation -> {
    //         latch.countDown();
    //         return null;
    //     }).when(mockComSI).notifyDbChange();

    //     // Start the watcher in a separate thread
    //     executor.submit(() -> watcher.watchFile(watchedFile.toString(), mockComSI));

    //     // Wait for the watcher to register
    //     TimeUnit.MILLISECONDS.sleep(200);
    //     tempDir.register(watchService, StandardWatchEventKinds.ENTRY_MODIFY);

    //     // Trigger file modification
    //     Files.writeString(watchedFile, "test");

    //     // Assert notifyDbChange was triggered
    //     boolean success = latch.await(3, TimeUnit.SECONDS);
    //     Assertions.assertTrue(success, "notifyDbChange should have been called");

    //     verify(mockComSI, atLeastOnce()).notifyDbChange();
    // }
}
