package fr.enedis.i2r.comsi.rest.controllers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import fr.enedis.i2r.comsi.errors.rest.InvalidRequestBodyException;
import fr.enedis.i2r.comsi.errors.rest.InvalidRequestHeadersException;
import fr.enedis.i2r.comsi.rest.requests.OrdreActivationBoitier;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;

public class StatutBoitierController {
    private BipStatusManager bipStatusManager;

    public StatutBoitierController(BipStatusManager bipStatusManager) {
        this.bipStatusManager = bipStatusManager;
    }

    public void changeStatus(Context ctx)
            throws InvalidRequestBodyException, JsonProcessingException,
            InvalidRequestHeadersException {
        OrdreActivationBoitier.MainData request = ctx.bodyAsClass(OrdreActivationBoitier.MainData.class);

        OrdreActivationBoitier.Body body = new ObjectMapper().readValue(request.body(),
                OrdreActivationBoitier.Body.class);

        int statusCode = body.state();
        String targetedAds = request.context().headers().ads();
        String targetedConfigurationHash = request.headers().xErdfHash();

        BipStatus requiredBipStatus = BipStatus.fromStatusCode(statusCode).orElseThrow(
                () -> new InvalidRequestBodyException(String.format("status du bip invalide : %d", statusCode)));

        bipStatusManager.changeBipStatus(requiredBipStatus, targetedAds, targetedConfigurationHash);

        ctx.status(HttpStatus.OK);
    }
}
