package fr.enedis.i2r.comsi.status;

import fr.enedis.i2r.comsi.errors.rest.InvalidRequestHeadersException;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.system.ports.SystemModulePort;

public class BipStatusManager {
    private ComsiParametersPort parametersPort;
    private BoardManagerPort boardManager;
    private SystemModulePort systemModule;

    public BipStatusManager(ComsiParametersPort parametersPort, BoardManagerPort boardManager, SystemModulePort system) {
        this.parametersPort = parametersPort;
        this.boardManager = boardManager;
        this.systemModule = system;
    }

    public void changeBipStatus(BipStatus newStatus, String targetedAds, String targetedConfigurationHash)
            throws InvalidRequestHeadersException {
        String bipAds = boardManager.getAds();

        if (!bipAds.equals(targetedAds)) {
            throw new InvalidRequestHeadersException(
                    String.format("l'ads ciblé (%s) ne correspond pas à celui du boitier (%s)", targetedAds, bipAds));
        }

        String bipConfigurationHash = parametersPort.getConfigurationHash();
        if (!bipConfigurationHash.equals(targetedConfigurationHash)) {
            throw new InvalidRequestHeadersException(
                    String.format("le hash de la configuration ciblé (%s) ne correspond pas à celui du boitier (%s)",
                            targetedConfigurationHash, bipConfigurationHash));
        }

        parametersPort.updateBipStatus(newStatus);
        systemModule.activateSecondaryServices();
        systemModule.startSecondaryServices();
    }

}
