package fr.enedis.i2r.comsi.rest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.errors.rest.ComsiError;
import fr.enedis.i2r.comsi.rest.controllers.LoggingController;
import fr.enedis.i2r.comsi.rest.controllers.MonitoringController;
import fr.enedis.i2r.comsi.rest.controllers.StatutBoitierController;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import fr.enedis.i2r.system.LoggingLoader;
import fr.enedis.i2r.system.ports.ShellExecutorPort;
import io.javalin.Javalin;
import io.javalin.config.JavalinConfig;
import io.javalin.openapi.plugin.OpenApiPlugin;
import io.javalin.openapi.plugin.swagger.SwaggerPlugin;

public class HttpServer implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(HttpServer.class);
    private static final Integer PORT = 8081;

    private BipStatusManager bipStatusManager;
    private ComSiConfiguration comSiConfiguration;
    private ShellExecutorPort shellExecutorPort;
    private LoggingLoader loggingLoader;

    public HttpServer(BipStatusManager bipStatusManager, ComSiConfiguration comSiConfiguration,
            ShellExecutorPort shellExecutorPort, LoggingLoader loggingLoader) {
        this.bipStatusManager = bipStatusManager;
        this.comSiConfiguration = comSiConfiguration;
        this.shellExecutorPort = shellExecutorPort;
        this.loggingLoader = loggingLoader;
    }

    @Override
    public void run() {
        logger.debug("Démarrage du serveur web...");
        Javalin app = Javalin.create(this::configureOpenApi);

        StatutBoitierController activationBoitierHandler = new StatutBoitierController(bipStatusManager);
        MonitoringController monitoringController = new MonitoringController(comSiConfiguration, shellExecutorPort);
        LoggingController loggingController = new LoggingController(this.loggingLoader);

        app.get(RestEndpoints.API_ROOT + RestEndpoints.TIME_SYNC, monitoringController::getTimeSync);
        app.post(RestEndpoints.API_ROOT + RestEndpoints.LOG_APP, loggingController::changeLogLevel);

        app.put(RestEndpoints.STATUS_CHANGE, activationBoitierHandler::changeStatus);

        app.exception(ComsiError.class, (e, ctx) -> {
            ctx.status(e.getStatusCode());
            ctx.result(String.format("erreur lors de votre requête à ComSI:\n%s", e.getMessage()));
        });
        app.start(PORT);

        logger.debug("REST Server has started and is running...");
        logger.info("Swagger docs at http://localhost:" + PORT + "/swagger");
        logger.info("OpenAPI JSON at http://localhost:" + PORT + "/openapi");
    }

    private void configureOpenApi(JavalinConfig config) {
        config.registerPlugin(new OpenApiPlugin(pluginConfig -> {
            pluginConfig.withDefinitionConfiguration((version, definition) -> {
                definition.withInfo(info -> {
                    info.setTitle("OpenAPI i2r");
                    info.setVersion("1.0.0");
                    info.setDescription("Documentation de l'API i2R");
                });
            });
        }));
        config.registerPlugin(new SwaggerPlugin());
        config.showJavalinBanner = false;
    }
}
