package fr.enedis.i2r.comsi.rest.requests;

import com.fasterxml.jackson.annotation.JsonProperty;

public class OrdreActivationBoitier {
    public record ContextHeaders(
            @JsonProperty("expires-at") String expiresAt,
            @JsonProperty("maVersion") String maVersion,
            @JsonProperty("ccma-correlation-id") String ccmaCorrelationId,
            @JsonProperty("maDateEmissionFlux") String maDateEmissionFlux,
            @JsonProperty("order-id") String orderId,
            @JsonProperty("order-type") String orderType,
            @JsonProperty("version") String version,
            @JsonProperty("maType") String maType,
            @JsonProperty("ads") String ads,
            @JsonProperty("maProcessId") String maProcessId,
            @JsonProperty("maAdsBoitier") String maAdsBoitier,
            @JsonProperty("ma-correlation-id") String maDashCorrelationId,
            @JsonProperty("maCorrelationId") String maCorrelationId,
            @JsonProperty("ma-date-emission-flux") String maDashDateEmissionFlux,
            @JsonProperty("maSource") String maSource) {
    }

    public record ContextData(
            @JsonProperty("headers") ContextHeaders headers,
            @JsonProperty("content") String content) {
    }

    public record RootHeaders(
            @JsonProperty("X-ERDF-API-VERSION") String xErdfApiVersion,
            @JsonProperty("X-ERDF-HASH") String xErdfHash) {
    }

    public record MainData(
            @JsonProperty("context") ContextData context,
            @JsonProperty("method") String method,
            @JsonProperty("headers") RootHeaders headers,
            @JsonProperty("body") String body,
            @JsonProperty("url_path") String urlPath) {
    }

    public record Body(
            @JsonProperty("name") String name,
            @JsonProperty("class") String className,
            @JsonProperty("state") int state) {
    }

}
