package fr.enedis.i2r.comsi;

import java.time.Clock;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.ModemManagerPort;
import fr.enedis.i2r.comsi.ports.ModuleSecuritePort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;

public class ComSI implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(ComSI.class);

    private ComSiConfiguration comSiConfiguration;
    private ComsiParametersPort parametersPort;
    private SiClientPort siClientPort;
    private BoardManagerPort boardManagerPort;
    private ModuleSecuritePort moduleSecuritePort;
    private ModemManagerPort modemManagerPort;
    private DatabaseUpdateWatcherPort dbUpdateWatcherPort;
    private Clock clock;

    public ComSI(ComSiConfiguration comSiConfiguration,
                 ComsiParametersPort parametersPort,
                 SiClientPort siClientPort,
                 BoardManagerPort boardManagerPort,
                 ModuleSecuritePort moduleSecuritePort,
                 ModemManagerPort modemManager,
                 DatabaseUpdateWatcherPort dbUpdateWatcherPort,
                 Clock clock) {

        this.comSiConfiguration = comSiConfiguration;
        this.parametersPort = parametersPort;
        this.siClientPort = siClientPort;
        this.boardManagerPort = boardManagerPort;
        this.moduleSecuritePort = moduleSecuritePort;
        this.modemManagerPort = modemManager;
        this.dbUpdateWatcherPort = dbUpdateWatcherPort;
        this.clock = clock;
    }

    @Override
    public void run() {
        try {
            logger.info("Running ComSi...");
            logger.debug("Sending configuration to iCOM");
            var ads = boardManagerPort.getAds();
            var idms = moduleSecuritePort.getIdms();
            var iccid = modemManagerPort.getIccid();

            ConfigurationBoitier config = ConfigurationBoitier.from(
                comSiConfiguration,
                clock.instant(), // TODO stubbing dates for now, replace by real values
                parametersPort.getConfigurationHash(),
                ads,
                idms,
                iccid
            );
            siClientPort.sendConfigurationBoitier(config);

            ConfigurationChangeWatcher configurationChangeWatcher = new ConfigurationChangeWatcher(dbUpdateWatcherPort, siClientPort.getSiConfigurationNotifier());
            ExecutorService executorService = Executors.newFixedThreadPool(1);
            executorService.submit(configurationChangeWatcher);
        } catch (Exception e) {
            logger.error("Erreur pendant le démarrage du module iCOM", e);
        }
    }

}
