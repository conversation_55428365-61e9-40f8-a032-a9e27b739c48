{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "urn:jsonschema:ConfigurationBoitierError:1.0", "$ref": "#/definitions/ConfigurationBoitierError", "title": "notif-echec-transformation-configuration-boitier", "definitions": {"ConfigurationBoitierError": {"$comment": "Headers CCMA standard (maType: ConfigurationBoitierError, maProcessId: CT0050)", "type": "object", "properties": {"origin": {"type": "string", "enum": ["ICOEUR"]}, "errors": {"type": "array", "items": {"type": "object", "properties": {"code": {"type": "string", "enum": ["EMPTY_FIELD", "INVALID_FIELD", "UNAUTHORIZED", "BAD_CONTENT", "UNDISCOVERED", "UNSUPPORTED_VERSION"]}, "field": {"type": ["string", "null"]}, "message": {"type": ["string", "null"]}}}}, "message": {"type": "object", "properties": {"body": {"type": "string"}, "headers": {"type": "object", "additionalProperties": true}}}}}}}