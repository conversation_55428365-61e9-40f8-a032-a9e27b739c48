{"$id": "urn:ConfigurationBoitier:2.0", "$schema": "http://json-schema.org/draft-07/schema#", "$ref": "#/definitions/ConfigurationBoitier", "definitions": {"ConfigurationBoitier": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"certificate": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"lastValidityCheck": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "nextValidityCheck": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "notAfter": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "ttl": {"type": "integer"}}}]}, "device": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"box": {"type": "object", "additionalProperties": true, "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"enable": {"type": "boolean"}, "status": {"type": ["integer", "null"]}, "IPsecEndPoint": {"type": "array", "items": {"type": ["string", "null"]}}}}]}, "ethernet": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"enable": {"type": "boolean"}, "ipv4Address": {"type": "object", "properties": {"enable": {"type": "integer"}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["string", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "ipv6Address": {"type": "object", "properties": {"enable": {"type": "integer"}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["integer", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "macAddress": {"type": ["string", "null"]}, "status": {"type": ["integer", "null"]}}}]}, "hard": {"type": "object", "additionalProperties": true, "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"ledLteLow": {"type": ["integer", "null"]}, "ledLteMed": {"type": ["integer", "null"]}, "ledLteHigh": {"type": ["integer", "null"]}, "adsid": {"type": ["string", "null"]}, "antenna": {"type": ["integer", "null"]}, "autotestResult": {"type": ["string", "null"]}, "batteryLevel": {"type": "integer"}, "cpuLevel": {"type": "integer"}, "currentTime": {"type": ["string", "null"]}, "dateResetBattery": {"type": ["string", "null"]}, "firstUseDate": {"type": ["string", "null"]}, "flashTotal": {"type": "integer"}, "iccid": {"type": ["string", "null"]}, "imei": {"type": ["string", "null"]}, "ledGprsHigh": {"type": "integer"}, "ledGprsLow": {"type": "integer"}, "ledGprsMed": {"type": "integer"}, "ledUmtsHigh": {"type": "integer"}, "ledUmtsLow": {"type": "integer"}, "ledUmtsMed": {"type": "integer"}, "manufacturer": {"type": ["string", "null"]}, "microCut": {"type": "integer"}, "partitions": {"type": "array", "items": {"type": "object", "properties": {"free": {"type": "integer"}, "fs": {"type": ["string", "null"]}, "mount": {"type": ["string", "null"]}, "name": {"type": ["string", "null"]}, "size": {"type": "integer"}}}}, "productClass": {"type": ["string", "null"]}, "ramLevel": {"type": "integer"}, "ramTotal": {"type": "integer"}, "serialNumber": {"type": ["string", "null"]}, "supplyType": {"type": ["integer", "null"]}, "uartSpeed": {"type": "integer"}, "upTime": {"type": ["integer", "null"]}}}]}, "meter": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"type": {"type": ["string", "null"]}}}]}, "modem": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"rsrp": {"type": ["integer", "null"]}, "rscp": {"type": ["integer", "null"]}, "rxLevel": {"type": ["integer", "null"]}, "techno": {"type": ["string", "null"]}}}]}, "ms": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"count": {"type": "integer"}, "cplc": {"type": ["string", "null"]}, "secConfig": {"type": ["string", "null"]}, "securityMod": {"type": ["string", "null"]}}}]}, "soft": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"applications": {"type": "array", "items": {"type": "object", "properties": {"installed": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "name": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}}}}, "initrd": {"type": "object", "properties": {"installed": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "name": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}}}, "kernel": {"type": "object", "properties": {"installed": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "name": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}}}, "rootfs": {"type": "array", "items": {"type": "object", "properties": {"installed": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "name": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}}}}}}]}}}]}, "dm": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"apn": {"type": "array", "items": {"type": "object", "properties": {"priority": {"type": "integer"}, "domain": {"type": ["string", "null"]}, "mcc": {"type": "string"}, "apname": {"type": "string"}, "mnc": {"type": "string"}}}}, "gzipLevel": {"type": "integer"}, "httpTmo": {"type": "integer"}, "linkType": {"type": "integer"}, "log": {"type": "object", "properties": {"depth": {"type": "integer"}, "dir": {"type": ["string", "null"]}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}, "nbBitsMasqueIPV4": {"type": "integer"}, "nbBitsMasqueIPV6": {"type": "integer"}, "pin": {"type": ["string", "null"]}, "pinEnabled": {"type": "boolean"}, "pingFreq": {"type": "integer"}, "pingIP1": {"type": "array", "items": {"type": ["string", "null"]}}, "pingIP2": {"type": "array", "items": {"type": ["string", "null"]}}, "pingTmo1": {"type": "integer"}, "pingTmo2": {"type": "integer"}, "reachable": {"type": "boolean"}, "resetTmo": {"type": "integer"}, "resetWanTmo": {"type": "integer"}, "retryFreq": {"type": "integer"}, "state": {"type": ["integer", "null"]}, "system": {"type": "integer"}, "tcpSynRetry": {"type": "integer"}, "validDate": {"type": ["string", "null"]}, "version": {"type": ["string", "null"]}, "hash": {"type": ["string", "null"]}}}]}, "flows": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"alarm": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}, "cfg": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}, "command": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}, "csr": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}, "ntp": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}, "reachable": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}, "stat": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}, "upgrade": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}, "waitprog": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}, "init": {"type": "object", "allOf": [{"$ref": "#/definitions/flow"}]}}}]}, "ppp": {"type": "object", "additionalProperties": true, "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"authentication": {"type": "integer"}, "currentMRU": {"type": ["integer", "null"]}, "enable": {"type": "boolean"}, "ipcpEnable": {"type": "boolean"}, "ipv4": {"type": "object", "properties": {"enable": {"type": "integer"}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["string", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "ipv6": {"type": "object", "properties": {"enable": {"type": "integer"}, "address": {"type": ["string", "null"]}, "subnet": {"type": ["integer", "null"]}, "gateway": {"type": ["string", "null"]}, "addressingType": {"type": ["string", "null"]}}}, "maxMRU": {"type": "integer"}, "maxMTU": {"type": "integer"}, "password": {"type": ["string", "null"]}, "reset": {"type": "boolean"}, "status": {"type": ["integer", "null"]}, "username": {"type": ["string", "null"]}}}]}, "services": {"type": "object", "additionalProperties": true, "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"callback": {"type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"authMtc": {"type": "boolean"}, "authSms": {"type": "boolean"}, "mtcCallback": {"type": "integer"}, "smsCallback": {"type": "integer"}, "whiteList": {"type": "array", "items": {"type": "string"}}}}]}, "log": {"type": "object", "allOf": [{"$ref": "#/definitions/service"}]}, "monitoring": {"type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"alarmsOn": {"type": "integer"}, "alarmsStat": {"type": ["string", "null"]}, "batteryDepth": {"type": "integer"}, "batteryFreq": {"type": "integer"}, "batteryOn": {"type": "integer"}, "batteryStat": {"type": ["string", "null"]}, "bootOn": {"type": "integer"}, "bootStat": {"type": ["string", "null"]}, "boxOn": {"type": "integer"}, "boxStat": {"type": ["string", "null"]}, "cellsFreq": {"type": "integer"}, "cellsInitFreq": {"type": "integer"}, "cellsInitNbSamples": {"type": "integer"}, "cellsNeighbours": {"type": "integer"}, "cellsOn": {"type": "integer"}, "cellsStat": {"type": ["string", "null"]}, "clockOn": {"type": "integer"}, "clockStat": {"type": ["string", "null"]}, "cpuFreq": {"type": "integer"}, "cpuOn": {"type": "integer"}, "cpuStat": {"type": ["string", "null"]}, "doorOn": {"type": "integer"}, "doorStat": {"type": ["string", "null"]}, "ethernetOn": {"type": "integer"}, "ethernetStat": {"type": ["string", "null"]}, "flashOn": {"type": "integer"}, "flashStat": {"type": ["string", "null"]}, "fsFreq": {"type": "integer"}, "fsOn": {"type": "integer"}, "fsStat": {"type": ["string", "null"]}, "logOn": {"type": "integer"}, "logStat": {"type": ["string", "null"]}, "meterOn": {"type": "integer"}, "meterStat": {"type": ["string", "null"]}, "msOn": {"type": "integer"}, "msStat": {"type": ["string", "null"]}, "networkDepth": {"type": "integer"}, "networkOn": {"type": "integer"}, "networkStat": {"type": ["string", "null"]}, "ramFreq": {"type": "integer"}, "ramOn": {"type": "integer"}, "ramStat": {"type": ["string", "null"]}, "securityOn": {"type": "integer"}, "securityStat": {"type": ["string", "null"]}, "simOn": {"type": "integer"}, "simStat": {"type": ["string", "null"]}, "supplyOn": {"type": "integer"}, "supplyStat": {"type": ["string", "null"]}, "ttl": {"type": "integer"}, "wanDepth": {"type": "integer"}, "wanOn": {"type": "integer"}, "wanStat": {"type": ["string", "null"]}}}]}, "ssh": {"type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"allowUsers": {"type": ["string", "null"]}, "deactivation": {"type": ["string", "null"]}, "listenAddress": {"type": "array", "items": {"type": "string"}}, "port": {"type": "integer"}, "sshFingerprint": {"type": ["string", "null"]}, "sshFingerprintType": {"type": "integer"}, "sshPublicKeyType": {"type": "integer"}, "umask": {"type": ["string", "null"]}}}]}, "supervision": {"type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"authKOAlarm": {"type": ["string", "null"]}, "authKODelay": {"type": "integer"}, "authKOLim": {"type": "integer"}, "authKOOn": {"type": "integer"}, "batteryAlarm": {"type": ["string", "null"]}, "batteryDelay": {"type": "integer"}, "batteryInf": {"type": "integer"}, "batteryLim": {"type": "integer"}, "batteryOn": {"type": "integer"}, "batterySup": {"type": "integer"}, "bootAlarm": {"type": ["string", "null"]}, "bootCause": {"type": "integer"}, "bootDelay": {"type": "integer"}, "bootLim": {"type": "integer"}, "bootOn": {"type": "integer"}, "boxAlarm": {"type": ["string", "null"]}, "boxDelay": {"type": "integer"}, "boxLim": {"type": "integer"}, "boxOn": {"type": "integer"}, "certInvalidAlarm": {"type": ["string", "null"]}, "certInvalidDelay": {"type": "integer"}, "certInvalidLim": {"type": "integer"}, "certInvalidOn": {"type": "integer"}, "certTTLAlarm": {"type": ["string", "null"]}, "certTTLDelay": {"type": "integer"}, "certTTLLim": {"type": "integer"}, "certTTLOn": {"type": "integer"}, "cpuAlarm": {"type": ["string", "null"]}, "cpuDelay": {"type": "integer"}, "cpuInf": {"type": "integer"}, "cpuLim": {"type": "integer"}, "cpuOn": {"type": "integer"}, "cpuSup": {"type": "integer"}, "doorAlarm": {"type": ["string", "null"]}, "doorDelay": {"type": "integer"}, "doorLim": {"type": "integer"}, "doorOn": {"type": "integer"}, "fileIntegrityKOAlarm": {"type": ["string", "null"]}, "fileIntegrityKODelay": {"type": "integer"}, "fileIntegrityKOLim": {"type": "integer"}, "fileIntegrityKOOn": {"type": "integer"}, "fileServerKOAlarm": {"type": ["string", "null"]}, "fileServerKODelay": {"type": "integer"}, "fileServerKOLim": {"type": "integer"}, "fileServerKOOn": {"type": "integer"}, "fsAlarm": {"type": ["string", "null"]}, "fsDelay": {"type": "integer"}, "fsInf": {"type": "integer"}, "fsLim": {"type": "integer"}, "fsOn": {"type": "integer"}, "fsSup": {"type": "integer"}, "keyKOAlarm": {"type": ["string", "null"]}, "keyKODelay": {"type": "integer"}, "keyKOLim": {"type": "integer"}, "keyKOOn": {"type": "integer"}, "meterAlarm": {"type": ["string", "null"]}, "meterDelay": {"type": "integer"}, "meterLim": {"type": "integer"}, "meterOn": {"type": "integer"}, "msAdminAlarm": {"type": ["string", "null"]}, "msAdminDelay": {"type": "integer"}, "msAdminLim": {"type": "integer"}, "msAdminOn": {"type": "integer"}, "ntpAlarm": {"type": ["string", "null"]}, "ntpDelay": {"type": "integer"}, "ntpLim": {"type": "integer"}, "ntpOn": {"type": "integer"}, "ramAlarm": {"type": ["string", "null"]}, "ramDelay": {"type": "integer"}, "ramInf": {"type": "integer"}, "ramLim": {"type": "integer"}, "ramOn": {"type": "integer"}, "ramSup": {"type": "integer"}, "restoredAlarm": {"type": ["string", "null"]}, "restoredDelay": {"type": "integer"}, "restoredLim": {"type": "integer"}, "restoredOn": {"type": "integer"}, "simAlarm": {"type": ["string", "null"]}, "simDelay": {"type": "integer"}, "simLim": {"type": "integer"}, "simOn": {"type": "integer"}, "sshAlarm": {"type": ["string", "null"]}, "sshDelay": {"type": "integer"}, "sshLim": {"type": "integer"}, "sshOn": {"type": "integer"}, "supplyAlarm": {"type": ["string", "null"]}, "supplyDelay": {"type": "integer"}, "supplyLim": {"type": "integer"}, "supplyOn": {"type": "integer"}, "ttl": {"type": "integer"}, "temperatureAlarm": {"type": ["string", "null"]}, "temperatureDelay": {"type": "integer"}, "temperatureLim": {"type": "integer"}, "temperatureOn": {"type": "integer"}}}]}, "time": {"type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"nbFailure": {"type": "integer"}, "ntpflow": {"type": ["string", "null"]}, "timeZone": {"type": ["string", "null"]}}}]}, "upgrade": {"type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"ttl": {"type": "integer"}}}]}, "web": {"oneOf": [{"type": "null"}, {"type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"ttl": {"type": "integer"}}}]}]}, "applicatifMetier": {"additionalProperties": true, "type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}}}]}, "passerelleSAPHIR": {"additionalProperties": true, "type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}}}]}, "passerellePMEPMI": {"additionalProperties": true, "type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}}}]}, "PasserelleICE": {"additionalProperties": true, "type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}}}]}, "passerelleICE": {"additionalProperties": true, "type": "object", "allOf": [{"$ref": "#/definitions/service"}, {"properties": {"log": {"type": "object", "additionalProperties": false, "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}}}]}}}]}, "ipsec": {"type": "object", "additionalProperties": true, "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"ipv4": {"type": "object", "additionalProperties": true}, "ipv6": {"type": "object", "additionalProperties": true}}}]}}}]}, "base": {"type": "object", "properties": {"cmdKey": {"type": ["string", "null"]}, "emitted": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "entity": {"type": ["string", "null"]}, "flow": {"type": ["string", "null"]}, "modified": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "pub": {"type": "boolean"}}}, "flow": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"freq1": {"type": "integer"}, "freq2": {"type": "integer"}, "lb": {"type": ["integer", "null"]}, "lbMax": {"type": ["integer", "null"]}, "retry1": {"type": "integer"}, "retry2": {"type": "integer"}, "slots": {"type": "array", "items": {"type": "object", "properties": {"day": {"type": "integer"}, "duration": {"type": "integer"}, "time": {"type": ["string", "null"]}}}}, "url1": {"type": "array", "items": {"type": ["string", "null"]}}, "url2": {"type": "array", "items": {"type": ["string", "null"]}}}}]}, "service": {"type": "object", "allOf": [{"$ref": "#/definitions/base"}, {"properties": {"status": {"type": ["string", "null"], "enum": ["STARTED", "STOPPED", "STARTING", "STOPPING"]}, "started": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "stopped": {"type": ["string", "null"], "pattern": "^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12][0-9]|3[01])T([0-1][0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])(\\.\\d+)?Z?$"}, "request": {"type": ["string", "null"], "enum": ["START", "STOP", "", null]}, "result": {"type": "integer"}, "error": {"type": ["string", "null"]}, "autostart": {"type": "boolean"}, "autostop": {"type": "integer"}, "log": {"type": "object", "properties": {"depth": {"type": "integer"}, "dir": {"type": "string"}, "level": {"type": "integer"}, "nb": {"type": "integer"}, "size": {"type": "integer"}}}}}]}}}