package fr.enedis.i2r.comsi.rest;

import static org.assertj.core.api.Assertions.assertThatExceptionOfType;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import fr.enedis.i2r.system.ports.SystemModulePort;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import fr.enedis.i2r.comsi.errors.rest.InvalidRequestBodyException;
import fr.enedis.i2r.comsi.errors.rest.InvalidRequestHeadersException;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.ComsiParametersPort;
import fr.enedis.i2r.comsi.rest.controllers.StatutBoitierController;
import fr.enedis.i2r.comsi.rest.requests.OrdreActivationBoitier;
import fr.enedis.i2r.comsi.rest.requests.OrdreActivationBoitier.ContextHeaders;
import fr.enedis.i2r.comsi.rest.requests.OrdreActivationBoitier.MainData;
import fr.enedis.i2r.comsi.rest.requests.OrdreActivationBoitier.RootHeaders;
import fr.enedis.i2r.comsi.status.BipStatus;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import io.javalin.http.Context;

public class ActivationBoitierTest {
    ComsiParametersPort parametersPort;
    BoardManagerPort boardManagerPort;
    SystemModulePort systemModulePort;
    Context ctx;

    @BeforeEach
    void setup() {
        parametersPort = mock(ComsiParametersPort.class);
        ctx = mock(Context.class);
        boardManagerPort = mock(BoardManagerPort.class);
        systemModulePort = mock(SystemModulePort.class);
    }

    @Test
    void le_boitier_passe_a_l_etat_stable_lorsqu_il_en_recoit_la_requete() throws Throwable {
        when(ctx.bodyAsClass(any())).thenReturn(testData("VERITABLEADS", 2, "VERITABLEHASH"));
        when(boardManagerPort.getAds()).thenReturn("VERITABLEADS");
        when(parametersPort.getConfigurationHash()).thenReturn("VERITABLEHASH");

        var controller = new StatutBoitierController(new BipStatusManager(parametersPort, boardManagerPort, systemModulePort));
        controller.changeStatus(ctx);

        verify(parametersPort, times(1)).updateBipStatus(BipStatus.STABLE);
    }

    @Test
    void on_ne_peut_pas_changer_le_statut_du_boitier_avec_des_valeurs_incoherentes() {
        when(boardManagerPort.getAds()).thenReturn("VERITABLEADS");
        when(parametersPort.getConfigurationHash()).thenReturn("VERITABLEHASH");

        when(ctx.bodyAsClass(any())).thenReturn(testData("VERITABLEADS", 3, "VERITABLEHASH"));

        var controller = new StatutBoitierController(new BipStatusManager(parametersPort, boardManagerPort, systemModulePort));
        assertThatExceptionOfType(InvalidRequestBodyException.class)
                .isThrownBy(() -> controller.changeStatus(ctx));

        verify(parametersPort, never()).updateBipStatus(BipStatus.STABLE);
    }

    @Test
    void l_ads_en_entete_doit_correspondre_a_celui_du_boitier() {
        when(boardManagerPort.getAds()).thenReturn("VERITABLEADS");
        when(parametersPort.getConfigurationHash()).thenReturn("VERITABLEHASH");

        when(ctx.bodyAsClass(any())).thenReturn(testData("MAUVAISADS", 2, "VERITABLEHASH"));

        var controller = new StatutBoitierController(new BipStatusManager(parametersPort, boardManagerPort, systemModulePort));

        assertThatExceptionOfType(InvalidRequestHeadersException.class).isThrownBy(
                () -> controller.changeStatus(ctx));
        verify(parametersPort, never()).updateBipStatus(BipStatus.STABLE);
    }

    @Test
    void le_hash_de_parametres_en_entete_doit_correspondre_a_celui_du_boitier() {
        when(boardManagerPort.getAds()).thenReturn("VERITABLEADS");
        when(parametersPort.getConfigurationHash()).thenReturn("VERITABLEHASH");

        when(ctx.bodyAsClass(any())).thenReturn(testData("VERITABLEADS", 2, "MAUVAISHASH"));

        var controller = new StatutBoitierController(new BipStatusManager(parametersPort, boardManagerPort, systemModulePort));

        assertThatExceptionOfType(InvalidRequestHeadersException.class).isThrownBy(
                () -> controller.changeStatus(ctx));
        verify(parametersPort, never()).updateBipStatus(BipStatus.STABLE);
    }

    private OrdreActivationBoitier.MainData testData(String ads, int state, String configHash) {
        return new MainData(
                new OrdreActivationBoitier.ContextData(
                        new ContextHeaders(
                                "2025-04-09T18:00:16.789943324Z",
                                "1.0",
                                "821ce772-9caa-4c14-bde8-859f332ef791",
                                "2025-04-09T14:00:16.789922013Z",
                                "821ce772-9caa-4c14-bde8-859f332ef791",
                                "InternalTeleparametrage",
                                "1.0",
                                "InitToStableOrder",
                                ads,
                                "CT0016",
                                ads,
                                "d4163f6c-59e3-4fc8-bc86-1aaddbedea66",
                                "d4163f6c-59e3-4fc8-bc86-1aaddbedea66",
                                "2025-04-09T14:00:16.789922013Z",
                                "ICU"),
                        String.format(
                                "{\"name\":\"cfg\",\"class\":\"Container\",\"objects\":[{\"name\":\"dm\",\"class\":\"DM\",\"state\":%d}]}",
                                state)),
                "PUT",
                new RootHeaders(
                        "2.0",
                        configHash),
                String.format("{\"name\":\"dm\",\"class\":\"DM\",\"state\":%d}", state),
                "/db/cfg/dm");
    }

}
