package fr.enedis.i2r.system;

import fr.enedis.i2r.system.ports.SystemdPort;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.system.leds.LedName;
import fr.enedis.i2r.system.leds.LedStatus;
import fr.enedis.i2r.system.ports.LedPort;
import fr.enedis.i2r.system.ports.SystemModulePort;

public class SystemModule implements Runnable, SystemModulePort {
    private static final Logger logger = LoggerFactory.getLogger(SystemModule.class);

    private final SystemConfiguration systemConfiguration;
    private final LedPort ledPort;
    private final SystemdPort systemdPort;

    public SystemModule(SystemConfiguration systemConfiguration, LedPort ledPort, SystemdPort systemdPort) {
        this.systemConfiguration = systemConfiguration;
        this.ledPort = ledPort;
        this.systemdPort = systemdPort;
    }

    @Override
    public void run() {
        logger.info("Running SystemModule...");
        turnOnPowerLed();
    }

    private void turnOnPowerLed() {
        logger.info("Turning on Power LED...");
        ledPort.setLedStatus(LedName.POWER, LedStatus.ON);
        logger.info("Power LED: {}", ledPort.getLedStatus(LedName.POWER));
    }

    @Override
    public void rebootBip() {
        logger.info("Reboot du bip demandé.");
    }

    @Override
    public void stopConnection() {
        logger.info("Arrêt de la connexion du boitier.");
    }

    @Override
    public void startConnection() {
        logger.info("Démarrage de la connexion.");
    }

    @Override
    public void setRslLed(LedStatus status) {
        ledPort.setLedStatus(LedName.RSL, status);
    }

    @Override
    public void activateSecondaryServices() {
        systemdPort.enableService(systemConfiguration.secondaryServices());
    }

    @Override
    public void startSecondaryServices() {
        systemdPort.startService(systemConfiguration.secondaryServices());
    }
}
