package fr.enedis.i2r.system;

import ch.qos.logback.classic.Level;

import java.util.Arrays;
import java.util.List;

public record SystemConfiguration(
    Level logLevel,
    List<String> secondaryServices) {

    public static final Level DEFAULT_LOG_LEVEL = Level.INFO;
    public static final List<String> DEFAULT_SECONDARY_SERVICES = Arrays.asList(
        "carbon-cache.service",
        "graphite-web.service",
        "i2r-monitoring.service",
        "telegraf.service"
    );

    public SystemConfiguration() {
        this(
            DEFAULT_LOG_LEVEL,
            DEFAULT_SECONDARY_SERVICES);
    }
}
