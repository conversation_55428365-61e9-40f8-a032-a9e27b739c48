{"version": "0.2.0", "configurations": [{"type": "java", "name": "SI Mock", "request": "launch", "mainClass": "fr.enedis.i2r.si.mock.Main", "projectName": "si-mock"}, {"type": "java", "name": "BG95 Serial Mock", "request": "launch", "mainClass": "fr.enedis.i2r.serial.mock.BG95SerialMock", "projectName": "hal-mock"}, {"type": "java", "name": "HAL Mock", "request": "launch", "mainClass": "fr.enedis.i2r.hal.mock.Main", "projectName": "hal-mock"}, {"type": "java", "name": "i2R App", "request": "launch", "mainClass": "fr.enedis.i2r.main.Main", "projectName": "main"}, {"type": "java", "name": "Current File", "request": "launch", "mainClass": "${file}"}]}