package fr.enedis.i2r.si.mock;

import static io.javalin.http.HttpStatus.OK;

import java.net.http.HttpClient;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.rest.RestEndpoints;
import io.javalin.Javalin;
import io.javalin.community.ssl.SslPlugin;
import io.javalin.config.JavalinConfig;
import io.javalin.openapi.plugin.OpenApiPlugin;
import io.javalin.openapi.plugin.swagger.SwaggerPlugin;

public class Main {

    private static final Logger logger = LoggerFactory.getLogger(Main.class);

    private static final Integer PORT = 8443;

    public static void main(String[] args) {
        logger.debug("Starting SI Mock...");
        Javalin app = Javalin.create(Main::configureHttpServer);
        HttpClient httpClient = HttpClient.newHttpClient();

        var siController = new SiController();
        var bipCaller = new BipCaller(httpClient);
        app.put(RestEndpoints.SI_CONFIG, ctx -> {
            siController.receiveConfiguration(ctx);
            if (ctx.status() == OK) {
                bipCaller.setStableStatus(ctx);
            }
        });
        app.start();

        logger.debug("REST Server has started and is running...");
        logger.info("Swagger docs at https://localhost:" + PORT + "/swagger");
        logger.info("OpenAPI JSON at https://localhost:" + PORT + "/openapi");

        System.out.println(" ▄▀▀ █    █▄ ▄█ ▄▀▄ ▄▀▀ █▄▀     ▄▀▄ █▄ █");
        System.out.println(" ▄██ █ ▀▀ █ ▀ █ ▀▄▀ ▀▄▄ █ █     ▀▄▀ █ ▀█");
    }

    private static void configureHttpServer(JavalinConfig config) {
        logger.debug("Configuring REST OpenAPI...");
        config.registerPlugin(new OpenApiPlugin(pluginConfig -> {
            pluginConfig.withDefinitionConfiguration((version, definition) -> {
                definition.withInfo(info -> {
                    info.setTitle("OpenAPI SI Mock");
                    info.setVersion("1.0.0");
                    info.setDescription("Documentation de l'API du Mock SI (iCom/iCoeur/iCare)");
                });
            });
        }));
        config.registerPlugin(new SwaggerPlugin());
        config.showJavalinBanner = false;

        SslPlugin sslPlugin = new SslPlugin(conf -> {
            conf.keystoreFromPath("/var/lib/i2r/certificate.jks", "password");
            conf.secure = true;
            conf.securePort = PORT;
            conf.insecure = false;
            conf.sniHostCheck = false;
            conf.http2 = true;
        });

        config.registerPlugin(sslPlugin);
    }
}
