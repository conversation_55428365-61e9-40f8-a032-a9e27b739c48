package fr.enedis.i2r.si.mock;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.zip.GZIPInputStream;

import fr.enedis.i2r.infra.rest.si.ConfigurationBoitierIcare;
import io.javalin.http.Context;

public class ExtractConfiguration {
    public static final String ZIP_FORMAT = "gzip";

    static ConfigurationBoitierIcare getConfigurationBoitier(Context ctx) {
        ConfigurationBoitierIcare config;
        try {
            if (ZIP_FORMAT.equalsIgnoreCase(ctx.header("Content-Encoding"))) {
                String decompressedJson = decompressGzip(ctx.bodyInputStream());
                config = ctx.jsonMapper().fromJsonString(decompressedJson, ConfigurationBoitierIcare.class);
            } else {
                config = ctx.bodyAsClass(ConfigurationBoitierIcare.class);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return config;
    }

    static String decompressGzip(InputStream compressedStream) throws IOException {
        try (GZIPInputStream gis = new GZIPInputStream(compressedStream);
                InputStreamReader reader = new InputStreamReader(gis, StandardCharsets.UTF_8);
                BufferedReader in = new BufferedReader(reader)) {

            StringBuilder jsonBuilder = new StringBuilder();
            String line;
            while ((line = in.readLine()) != null) {
                jsonBuilder.append(line);
            }
            return jsonBuilder.toString();
        }
    }
}
