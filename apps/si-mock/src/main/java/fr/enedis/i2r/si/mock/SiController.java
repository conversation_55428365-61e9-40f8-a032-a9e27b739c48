package fr.enedis.i2r.si.mock;

import java.io.IOException;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import fr.enedis.i2r.comsi.rest.RestEndpoints;
import fr.enedis.i2r.infra.rest.si.ConfigurationBoitierIcare;
import fr.enedis.i2r.infra.rest.si.error.ConfigurationBoitierError;
import fr.enedis.i2r.infra.rest.si.error.ErrorCode;
import fr.enedis.i2r.infra.rest.si.error.ErrorDetail;
import fr.enedis.i2r.infra.rest.si.error.Message;
import fr.enedis.i2r.infra.rest.si.error.Origin;
import io.javalin.http.Context;
import io.javalin.http.HttpStatus;
import io.javalin.openapi.HttpMethod;
import io.javalin.openapi.OpenApi;
import io.javalin.openapi.OpenApiContent;
import io.javalin.openapi.OpenApiParam;
import io.javalin.openapi.OpenApiRequestBody;
import io.javalin.openapi.OpenApiResponse;

import static fr.enedis.i2r.si.mock.ExtractConfiguration.ZIP_FORMAT;

public class SiController {
    private static final Logger logger = LoggerFactory.getLogger(SiController.class);

    private static final int IDMS_LENGTH = 32;

    @OpenApi(
        summary = "Reçoit et valide la configuration du boitier par le SI",
        operationId = "receiveConfiguration",
        path = RestEndpoints.SI_CONFIG,
        methods = HttpMethod.PUT,
        tags = { "Configuration" },
        pathParams = {
            @OpenApiParam(
                name = "idms",
                type = String.class,
                required = true,
                description = "ID Module Sécurité"
            )
        },
        requestBody = @OpenApiRequestBody(
                    content = {
                            @OpenApiContent(from = ConfigurationBoitierIcare.class) },
                    required = true,
            description = "Configuration du boitier"
        ),
        responses = {
            @OpenApiResponse(
                status = "200",
                description = "Configuration valide",
                content = { @OpenApiContent(from = String.class) }
            ),
            @OpenApiResponse(
                status = "400",
                description = "IDMS manquant",
                content = { @OpenApiContent(from = String.class) }
            ),
            @OpenApiResponse(
                status = "500",
                description = "Configuration invalide",
                content = { @OpenApiContent(from = String.class) }
            )
        }
    )

    public void receiveConfiguration(Context ctx) {

        logger.info("Receive configuration");
        logger.info("idms : {}", ctx.pathParam("idms"));
        logger.info("");
        if (ctx.pathParam("idms").length() != IDMS_LENGTH) {
            ctx.status(HttpStatus.BAD_REQUEST.getCode()).result("IDMS is invalid");
            return;
        }


        String config;
        if (ZIP_FORMAT.equalsIgnoreCase(ctx.header("Content-Encoding"))) {
            try {
                config = ExtractConfiguration.decompressGzip(ctx.bodyInputStream());
            } catch (IOException e) {
                ctx.status(HttpStatus.BAD_REQUEST.getCode()).result("Invalid body data.");
                return;
            }
        } else {
            config = ctx.body();
        }
        logger.info(config);

        HttpStatus status;
        String result = "";

        if (ConfigurationValidator.validateConfigurationBoitier(config)) {
            status = HttpStatus.OK;
            result = HttpStatus.OK.toString();
        } else {
            status = HttpStatus.SERVICE_UNAVAILABLE;
            result = buildError("Configuration boitier invalide.");
        }
        ctx.status(status).result(result);
    }

    private static String buildError(String errorMessage) {
        String jsonResponse = "";
        try {
            var errorDetail = new ErrorDetail();
            errorDetail.setCode(ErrorCode.BAD_CONTENT);
            errorDetail.setMessage(errorMessage);
            var message = new Message();
            message.setBody("Error SI Mock");
            var error = new ConfigurationBoitierError();
            error.setOrigin(Origin.ICOEUR);
            error.setMessage(message);
            error.setErrors(List.of(errorDetail));

            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
            jsonResponse = objectMapper.writeValueAsString(error);
            return jsonResponse;
        } catch (Exception ex) {
            // Do nothing
        }
        return jsonResponse;
    }

}
