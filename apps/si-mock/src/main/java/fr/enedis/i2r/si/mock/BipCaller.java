package fr.enedis.i2r.si.mock;

import static fr.enedis.i2r.comsi.rest.RestEndpoints.STATUS_CHANGE;
import static fr.enedis.i2r.comsi.status.BipStatus.STABLE;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.databind.ObjectMapper;

import fr.enedis.i2r.comsi.IpAddress;
import fr.enedis.i2r.comsi.rest.requests.OrdreActivationBoitier;
import fr.enedis.i2r.infra.rest.SiHeaders;
import io.javalin.http.Context;

public class BipCaller {
    private static final Logger logger = LoggerFactory.getLogger(BipCaller.class);

    HttpClient client;

    public BipCaller(HttpClient client) {
        this.client = client;
    }

    public void setStableStatus(Context ctx) throws IOException, InterruptedException {
        String ads = Optional.ofNullable(ctx.header(SiHeaders.MA_ADS_BOITER)).orElseThrow();
        String configHash = Optional.ofNullable(ctx.header(SiHeaders.X_ERDF_HASH_HEADER)).orElseThrow();

        IpAddress bipIp = new IpAddress("127.0.0.1");
        int bipPort = 8081;

        logger.info("Send setStableStatus");
        logger.info("Ads : {}", ads);
        logger.info("Hash : {}", configHash);
        logger.info("");

        var payload = new OrdreActivationBoitier.MainData(
            new OrdreActivationBoitier.ContextData(
                new OrdreActivationBoitier.ContextHeaders(
                    "2025-04-09T18:00:16.789943324Z",
                    "1.0",
                    "821ce772-9caa-4c14-bde8-859f332ef791",
                    "2025-04-09T14:00:16.789922013Z",
                    "821ce772-9caa-4c14-bde8-859f332ef791",
                    "InternalTeleparametrage",
                    "1.0",
                    "InitToStableOrder",
                    ads,
                    "CT0016",
                    ads,
                    "d4163f6c-59e3-4fc8-bc86-1aaddbedea66",
                    "d4163f6c-59e3-4fc8-bc86-1aaddbedea66",
                    "2025-04-09T14:00:16.789922013Z",
                    "ICU"),
                String.format(
                    "{\"name\":\"cfg\",\"class\":\"Container\",\"objects\":[{\"name\":\"dm\",\"class\":\"DM\",\"state\":%d}]}",
                    STABLE.statusCode)),
            "PUT",
            new OrdreActivationBoitier.RootHeaders(
                "2.0",
                configHash),
            String.format("{\"name\":\"dm\",\"class\":\"DM\",\"state\":%d}", STABLE.statusCode),
            "/db/cfg/dm");

        ObjectMapper mapper = new ObjectMapper();
        String jsonPayload = mapper.writeValueAsString(payload);
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://" + bipIp + ":" + bipPort + STATUS_CHANGE))
                .header("Accept", "application/json, text/plain")
                .header("Content-Type", "application/json")
                .PUT(HttpRequest.BodyPublishers.ofString(jsonPayload))
                .build();

        HttpResponse<String> response = client.send(request, BodyHandlers.ofString());

        logger.info("Status : {}", response.statusCode());
        logger.info("Contenu : {}", response.body());
        logger.info("Content-Type : {}", response.headers().firstValue("Content-Type").orElse("inconnu"));
    }
}
