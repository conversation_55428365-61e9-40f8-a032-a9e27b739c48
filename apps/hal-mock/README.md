
Projet servant à simuler le hardware en local (HAL et Modem)

# HAL Mock

### Prérequis

Avoir les fichiers de configurations dans leur dossier respectif, à savoir :

* ``/etc/dbus-1/system.d/`` pour les fichiers ``*.conf``
* ``/usr/share/dbus-1/system-services/`` pour les fichiers ``*.services``

### Génération des fichiers de configuration

Il y a 2 manières pour que les fichiers de configuration de DBUS soient correctement en place:

1) A la création du devcontainer, la configuration du DBus est récupérée du dossier ``.devcontainer/dbus``, pour être déplacer dans les dossiers nécessaire.
2) Régénérer les fichiers de configurations à l'aide du projet "hal-mock-config" et les déplacer dans les bons dossiers. (Nécessaire si les fichiers du repo
   dans le dossier ``.devcontainer/dbus`` ne sont plus correct)

