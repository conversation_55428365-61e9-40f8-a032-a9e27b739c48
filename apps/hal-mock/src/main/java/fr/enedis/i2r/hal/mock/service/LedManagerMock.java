package fr.enedis.i2r.hal.mock.service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.hal.LEDManager1;
import fr.enedis.i2r.system.leds.LedStatus;

public class LedManagerMock implements LEDManager1 {

    private static final Logger logger = LoggerFactory.getLogger(LedManagerMock.class);
    private static final File ledsStateLogFile = new File("/var/log/i2r/leds.log");

    private byte powerLedStatus = 0;
    private byte cptLedStatus = 0;
    private byte eth1LedStatus = 0;
    private byte eth2LedStatus = 0;
    private byte siLedStatus = 0;
    private byte rslLedStatus = 0;

    public LedManagerMock() {
        try {
            if (ledsStateLogFile.exists()) {
                boolean fileDeleted = ledsStateLogFile.delete();

                if (!fileDeleted) {
                    logger.error("Echec lors de la suppression du fichier de log");
                }
            }
            boolean fileCreated = ledsStateLogFile.createNewFile();
            if (!fileCreated) {
                logger.error("Echec lors de la création du fichier de log");
            }

        } catch (IOException e) {
            logger.error("Failed to create LEDs log file", e);
        }
    }

    @Override
    public void setPowerLedStatus(byte status) {
        logger.info("Setting Power LED to {}", LedStatus.fromByte(status).orElseThrow());
        powerLedStatus = status;
        logCurrentState();
    }

    @Override
    public void setCLedStatus(byte cLedStatus) {
        logger.info("Setting C LED to {}", LedStatus.fromByte(cLedStatus).orElseThrow());
        powerLedStatus = cLedStatus;
        logCurrentState();
    }

    @Override
    public byte getCLedStatus() {
        logger.info("Getting CPT LED status: {}", LedStatus.fromByte(cptLedStatus).orElseThrow());
        return cptLedStatus;
    }

    @Override
    public void setEth1LedStatus(byte status) {
        logger.info("Setting Eth1 LED to {}", LedStatus.fromByte(status).orElseThrow());
        eth1LedStatus = status;
        logCurrentState();
    }

    @Override
    public void setEth2LedStatus(byte status) {
        logger.info("Setting Eth2 LED to {}", LedStatus.fromByte(status).orElseThrow());
        eth2LedStatus = status;
        logCurrentState();
    }

    @Override
    public void setSILedStatus(byte status) {
        logger.info("Setting SI LED to {}", LedStatus.fromByte(status).orElseThrow());
        siLedStatus = status;
        logCurrentState();
    }

    @Override
    public byte getRSLLedStatusAndColor() {
        logger.info("Getting RSL LED status: {}", LedStatus.fromByte(rslLedStatus).orElseThrow());
        return rslLedStatus;
    }

    @Override
    public void setRSLLedStatusAndColor(byte rSLLedStatusAndColor) {
        logger.info("Setting RSL LED to {}", LedStatus.fromByte(rSLLedStatusAndColor).orElseThrow());
        rslLedStatus = rSLLedStatusAndColor;
        logCurrentState();
    }

    @Override
    public byte getPowerLedStatus() {
        logger.info("Getting Power LED status: {}", LedStatus.fromByte(powerLedStatus).orElseThrow());
        return powerLedStatus;
    }

    @Override
    public byte getEth1LedStatus() {
        logger.info("Getting Eth1 LED status: {}", LedStatus.fromByte(eth1LedStatus).orElseThrow());
        return eth1LedStatus;
    }

    @Override
    public byte getEth2LedStatus() {
        logger.info("Getting Eth2 LED status: {}", LedStatus.fromByte(eth2LedStatus).orElseThrow());
        return eth2LedStatus;
    }

    @Override
    public byte getSILedStatus() {
        logger.info("Getting SI LED status: {}", LedStatus.fromByte(siLedStatus).orElseThrow());
        return siLedStatus;
    }

    private void logCurrentState() {
        String currentDateTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        String powerLedColor = (powerLedStatus != 0) ? "GREEN" : "";
        String cptLedColor = (cptLedStatus != 0) ? "YELLOW" : "";
        String eth1LedColor = (eth1LedStatus != 0) ? "YELLOW" : "";
        String eth2LedColor = (eth2LedStatus != 0) ? "RED" : "";
        String siLedColor = (siLedStatus != 0) ? "GREEN" : "";
        String rslLedColor = (rslLedStatus != 0) ? LedStatus.fromByte(rslLedStatus).get().name() : "OFF_GRAY";

        try (FileWriter writer = new FileWriter(ledsStateLogFile, true)) {
            writer.write("--------------------\n"); // Separator
            writer.write(currentDateTime + "\n");
            writer.write("LED POWER " + LedStatus.fromByte(powerLedStatus).get() + " " + powerLedColor + "\n");
            writer.write("LED CPT " + LedStatus.fromByte(cptLedStatus).get() + " " + cptLedColor + "\n");
            writer.write("LED ETH1 " + LedStatus.fromByte(eth1LedStatus).get() + " " + eth1LedColor + "\n");
            writer.write("LED ETH2 " + LedStatus.fromByte(eth2LedStatus).get() + " " + eth2LedColor + "\n");
            writer.write("LED SI " + LedStatus.fromByte(siLedStatus).get() + " " + siLedColor + "\n");
            writer.write("LED RSL " + rslLedColor.split("_")[0] + " " + rslLedColor.split("_")[1] + "\n");
        } catch (IOException e) {
            logger.error("Failed to write to LEDs log file", e);
        }
    }

    @Override
    public String getObjectPath() {
        return "/fr/enedis/HAL/LEDManager1";
    }

}
