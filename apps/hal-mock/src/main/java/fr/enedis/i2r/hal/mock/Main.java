package fr.enedis.i2r.hal.mock;

import fr.enedis.hal.BoardManager1;
import fr.enedis.i2r.hal.mock.service.BoardManagerMock;
import fr.enedis.i2r.hal.mock.systemd.SystemdManagerMock;
import org.freedesktop.dbus.connections.impl.DBusConnection;
import org.freedesktop.dbus.connections.impl.DBusConnectionBuilder;
import org.freedesktop.systemd1.Manager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.hal.LEDManager1;
import fr.enedis.hal.ModemManager1;
import fr.enedis.i2r.hal.mock.service.LedManagerMock;
import fr.enedis.i2r.hal.mock.service.ModemManagerMock;
import fr.enedis.i2r.system.leds.LedStatus;

public class Main {

    private static final Logger logger = LoggerFactory.getLogger(Main.class);

    public static void main(String[] args) {
        logger.info("Starting HAL Mock...");

        try (DBusConnection connection = DBusConnectionBuilder.forSystemBus().build()) {

            logger.info("Starting LedManagerMock");
            LEDManager1 ledManager = new LedManagerMock();
            connection.requestBusName("fr.enedis.HAL.LEDManager1");
            connection.exportObject(ledManager.getObjectPath(), ledManager);

            logger.info("Starting ModemManagerMock");
            ModemManager1 modemManager = new ModemManagerMock();
            connection.requestBusName("fr.enedis.HAL.ModemManager1");
            connection.exportObject(modemManager.getObjectPath(), modemManager);

            logger.info("Starting BoardManagerMock");
            BoardManager1 boardManager = new BoardManagerMock();
            connection.requestBusName("fr.enedis.HAL.BoardManager1");
            connection.exportObject(boardManager.getObjectPath(), boardManager);

            logger.info("Starting SystemdManagerMock");
            Manager systemdManager = new SystemdManagerMock();
            connection.requestBusName("org.freedesktop.systemd1");
            connection.exportObject(systemdManager.getObjectPath(), systemdManager);

            logger.info("HAL Mock is running...");
            System.out.println(" █▄█ ▄▀▄ █     █▄ ▄█ ▄▀▄ ▄▀▀ █▄▀     ▄▀▄ █▄ █");
            System.out.println(" █ █ █▀█ █▄▄   █ ▀ █ ▀▄▀ ▀▄▄ █ █     ▀▄▀ █ ▀█");

            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                logger.info("HAL Mock is shutting down...");
                turnOffAllLeds(ledManager);
                logger.info("HAL Mock is shutdown");
            }));

            while (true) {
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            logger.error("Error while starting HAL Mock", e);
            e.printStackTrace();
        }
    }

    private static void turnOffAllLeds(LEDManager1 ledManager) {
        ledManager.setPowerLedStatus(LedStatus.OFF.getValue());
        ledManager.setCLedStatus(LedStatus.OFF.getValue());
        ledManager.setEth1LedStatus(LedStatus.OFF.getValue());
        ledManager.setEth2LedStatus(LedStatus.OFF.getValue());
        ledManager.setSILedStatus(LedStatus.OFF.getValue());
        ledManager.setRSLLedStatusAndColor(LedStatus.OFF.getValue());
    }
}
