package fr.enedis.i2r.hal.mock.service;

import java.util.List;
import java.util.Random;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.hal.CurrentOperatorTuple;
import fr.enedis.hal.CurrentSignalTuple;
import fr.enedis.hal.ModemManager1;

public class ModemManagerMock implements ModemManager1 {

    private static final Logger logger = LoggerFactory.getLogger(ModemManagerMock.class);

    private boolean modemStatus = false;
    private boolean hibernateMode = false;
    private static final String IMEI = "MockIMEI123456789";
    private static final String ICCID = "MockICCID987654321";
    private static final String manufacturer = "MockManufactor";
    private static final String model = "MockModel";
    private static final String firmwareVersion = "MockFirmwareVersion";

    @Override
    public void RebootModem() {
        logger.info("RebootModem called");
    }

    @Override
    public String SendATCommand(String commandeAt) {
        logger.info("SendATCommand called with ATCommand: {}", commandeAt);

        switch (commandeAt) {
            case "AT+CGDCONT?":
                return "\r\n1,pdp_type,apn,ppd_addr,data_comp,head_comp,ip\r\n\r\nOK\r\n";
            case "AT+CSQ":
                return "\r\n+CSQ: " + generateRandomSignalInDb().toString() + ",99\r\n\r\nOK\r\n";
        }

        return "\r\nOK\r\n";
    }

    private Short generateRandomSignalInDb() {
        List<Integer> possibleValues = List.of(-99, -89, -79, -69);
        Random rand = new Random();
        return possibleValues.get(rand.nextInt(possibleValues.size())).shortValue();
    }

    @Override
    public String getIMEI() {
        logger.info("getIMEI called");
        logger.info("IMEI: " + IMEI);
        return IMEI;
    }

    @Override
    public String getICCID() {
        logger.info("getICCID called");
        logger.info("ICCID: " + ICCID);
        return ICCID;
    }

    @Override
    public boolean isModemStatus() {
        logger.info("isModemStatus called");
        logger.info("ModemStatus: {}", modemStatus);
        return modemStatus;
    }

    @Override
    public boolean isHibernateMode() {
        logger.info("isHibernateMode called");
        logger.info("HibernateMode: {}", hibernateMode);
        return hibernateMode;
    }

    @Override
    public void setHibernateMode(boolean hibernateMode) {
        logger.info("setHibernateMode called with hibernateMode: {}", hibernateMode);
        this.hibernateMode = hibernateMode;
    }

    @Override
    public String getManufacturer() {
        logger.info("getManufactor called");
        logger.info("Manufacturer: " + manufacturer);
        return manufacturer;
    }

    @Override
    public String getModel() {
        logger.info("getModel called");
        logger.info("Model: " + model);
        return model;
    }

    @Override
    public String getFirmwareVersion() {
        logger.info("getFirmwareVersion called");
        logger.info("FirmwareVersion: " + firmwareVersion);
        return firmwareVersion;
    }

    @Override
    public String getObjectPath() {
        return "/fr/enedis/HAL/ModemManager1";
    }

    @Override
    public CurrentOperatorTuple CurrentOperator() {
        logger.info("currentOperator appelé");
        return new CurrentOperatorTuple(true, "ORANGE");
    }

    @Override
    public boolean Connect(String apn, String login, String password) {
        logger.info("connect appelé");
        return true;
    }

    @Override
    public CurrentSignalTuple CurrentSignal() {
        logger.info("CurrentSignal appelé");
        return new CurrentSignalTuple(true, generateRandomSignalInDb());
    }

}
