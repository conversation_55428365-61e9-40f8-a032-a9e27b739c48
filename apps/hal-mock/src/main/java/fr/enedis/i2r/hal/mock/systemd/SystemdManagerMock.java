package fr.enedis.i2r.hal.mock.systemd;

import java.util.List;

import org.freedesktop.dbus.DBusPath;
import org.freedesktop.dbus.FileDescriptor;
import org.freedesktop.dbus.annotations.DBusInterfaceName;
import org.freedesktop.dbus.types.UInt32;
import org.freedesktop.dbus.types.UInt64;
import org.freedesktop.systemd1.AddDependencyUnitFilesChangesStruct;
import org.freedesktop.systemd1.DisableUnitFilesChangesStruct;
import org.freedesktop.systemd1.DisableUnitFilesWithFlagsAndInstallInfoTuple;
import org.freedesktop.systemd1.DisableUnitFilesWithFlagsChangesStruct;
import org.freedesktop.systemd1.DumpUnitFileDescriptorStoreEntriesStruct;
import org.freedesktop.systemd1.EnableUnitFilesChangesStruct;
import org.freedesktop.systemd1.EnableUnitFilesTuple;
import org.freedesktop.systemd1.EnableUnitFilesWithFlagsTuple;
import org.freedesktop.systemd1.EnqueueUnitJobTuple;
import org.freedesktop.systemd1.GetDynamicUsersUsersStruct;
import org.freedesktop.systemd1.GetJobAfterJobsStruct;
import org.freedesktop.systemd1.GetJobBeforeJobsStruct;
import org.freedesktop.systemd1.GetUnitByPIDFDTuple;
import org.freedesktop.systemd1.GetUnitProcessesProcessesStruct;
import org.freedesktop.systemd1.LinkUnitFilesChangesStruct;
import org.freedesktop.systemd1.ListJobsJobsStruct;
import org.freedesktop.systemd1.ListUnitFilesByPatternsUnitFilesStruct;
import org.freedesktop.systemd1.ListUnitFilesUnitFilesStruct;
import org.freedesktop.systemd1.ListUnitsByNamesUnitsStruct;
import org.freedesktop.systemd1.ListUnitsByPatternsUnitsStruct;
import org.freedesktop.systemd1.ListUnitsFilteredUnitsStruct;
import org.freedesktop.systemd1.ListUnitsUnitsStruct;
import org.freedesktop.systemd1.Manager;
import org.freedesktop.systemd1.MaskUnitFilesChangesStruct;
import org.freedesktop.systemd1.MountImageUnitOptionsStruct;
import org.freedesktop.systemd1.PresetAllUnitFilesChangesStruct;
import org.freedesktop.systemd1.PresetUnitFilesTuple;
import org.freedesktop.systemd1.PresetUnitFilesWithModeTuple;
import org.freedesktop.systemd1.ReenableUnitFilesTuple;
import org.freedesktop.systemd1.RevertUnitFilesChangesStruct;
import org.freedesktop.systemd1.SetDefaultTargetChangesStruct;
import org.freedesktop.systemd1.SetUnitPropertiesPropertiesStruct;
import org.freedesktop.systemd1.StartTransientUnitAuxStruct;
import org.freedesktop.systemd1.StartTransientUnitPropertiesStruct;
import org.freedesktop.systemd1.UnmaskUnitFilesChangesStruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@DBusInterfaceName("org.freedesktop.systemd1")
public class SystemdManagerMock implements Manager {
    private static final Logger logger = LoggerFactory.getLogger(SystemdManagerMock.class);

    @Override
    public String getObjectPath() {
        return "/org/freedesktop/systemd1";
    }

    @Override
    public EnableUnitFilesTuple EnableUnitFiles(List<String> list, boolean b, boolean b1) {
        String scope = b?"runtime":"permanent";
        String symlinkReplacement = b1?"symlink replacement":"no symlink replacement";
        List<EnableUnitFilesChangesStruct> ret = new java.util.ArrayList<>(List.of());
        for(String service : list) {
            logger.info("EnableUnitFiles called for service <{}> for <{}> with <{}>" , service , scope, symlinkReplacement  );
            ret.add(new EnableUnitFilesChangesStruct("type", "symlinkName"+service, "symlinkDestination"+service));
        }
        return new EnableUnitFilesTuple(false, null);
    }

    @Override
    public DBusPath StartUnit(String s, String s1) {
        logger.info("StartUnit called for service <{}> with mode <{}>" , s , s1);
        return new DBusPath("pathOf"+s);
    }

    @Override
    public String getVersion() {
        return "";
    }

    @Override
    public String getFeatures() {
        return "";
    }

    @Override
    public String getVirtualization() {
        return "";
    }

    @Override
    public String getConfidentialVirtualization() {
        return "";
    }

    @Override
    public String getArchitecture() {
        return "";
    }

    @Override
    public String getTainted() {
        return "";
    }

    @Override
    public UInt64 getFirmwareTimestamp() {
        return null;
    }

    @Override
    public UInt64 getFirmwareTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getLoaderTimestamp() {
        return null;
    }

    @Override
    public UInt64 getLoaderTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getKernelTimestamp() {
        return null;
    }

    @Override
    public UInt64 getKernelTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getInitRDTimestamp() {
        return null;
    }

    @Override
    public UInt64 getInitRDTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getUserspaceTimestamp() {
        return null;
    }

    @Override
    public UInt64 getUserspaceTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getFinishTimestamp() {
        return null;
    }

    @Override
    public UInt64 getFinishTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getSecurityStartTimestamp() {
        return null;
    }

    @Override
    public UInt64 getSecurityStartTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getSecurityFinishTimestamp() {
        return null;
    }

    @Override
    public UInt64 getSecurityFinishTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getGeneratorsStartTimestamp() {
        return null;
    }

    @Override
    public UInt64 getGeneratorsStartTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getGeneratorsFinishTimestamp() {
        return null;
    }

    @Override
    public UInt64 getGeneratorsFinishTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getUnitsLoadStartTimestamp() {
        return null;
    }

    @Override
    public UInt64 getUnitsLoadStartTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getUnitsLoadFinishTimestamp() {
        return null;
    }

    @Override
    public UInt64 getUnitsLoadFinishTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getUnitsLoadTimestamp() {
        return null;
    }

    @Override
    public UInt64 getUnitsLoadTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getInitRDSecurityStartTimestamp() {
        return null;
    }

    @Override
    public UInt64 getInitRDSecurityStartTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getInitRDSecurityFinishTimestamp() {
        return null;
    }

    @Override
    public UInt64 getInitRDSecurityFinishTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getInitRDGeneratorsStartTimestamp() {
        return null;
    }

    @Override
    public UInt64 getInitRDGeneratorsStartTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getInitRDGeneratorsFinishTimestamp() {
        return null;
    }

    @Override
    public UInt64 getInitRDGeneratorsFinishTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getInitRDUnitsLoadStartTimestamp() {
        return null;
    }

    @Override
    public UInt64 getInitRDUnitsLoadStartTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getInitRDUnitsLoadFinishTimestamp() {
        return null;
    }

    @Override
    public UInt64 getInitRDUnitsLoadFinishTimestampMonotonic() {
        return null;
    }

    @Override
    public String getLogLevel() {
        return "";
    }

    @Override
    public void setLogLevel(String s) {

    }

    @Override
    public String getLogTarget() {
        return "";
    }

    @Override
    public void setLogTarget(String s) {

    }

    @Override
    public UInt32 getNNames() {
        return null;
    }

    @Override
    public UInt32 getNFailedUnits() {
        return null;
    }

    @Override
    public UInt32 getNJobs() {
        return null;
    }

    @Override
    public UInt32 getNInstalledJobs() {
        return null;
    }

    @Override
    public UInt32 getNFailedJobs() {
        return null;
    }

    @Override
    public double getProgress() {
        return 0;
    }

    @Override
    public List<String> getEnvironment() {
        return List.of();
    }

    @Override
    public boolean isConfirmSpawn() {
        return false;
    }

    @Override
    public boolean isShowStatus() {
        return false;
    }

    @Override
    public List<String> getUnitPath() {
        return List.of();
    }

    @Override
    public String getDefaultStandardOutput() {
        return "";
    }

    @Override
    public String getDefaultStandardError() {
        return "";
    }

    @Override
    public String getWatchdogDevice() {
        return "";
    }

    @Override
    public UInt64 getWatchdogLastPingTimestamp() {
        return null;
    }

    @Override
    public UInt64 getWatchdogLastPingTimestampMonotonic() {
        return null;
    }

    @Override
    public UInt64 getRuntimeWatchdogUSec() {
        return null;
    }

    @Override
    public void setRuntimeWatchdogUSec(UInt64 uInt64) {

    }

    @Override
    public UInt64 getRuntimeWatchdogPreUSec() {
        return null;
    }

    @Override
    public void setRuntimeWatchdogPreUSec(UInt64 uInt64) {

    }

    @Override
    public String getRuntimeWatchdogPreGovernor() {
        return "";
    }

    @Override
    public void setRuntimeWatchdogPreGovernor(String s) {

    }

    @Override
    public UInt64 getRebootWatchdogUSec() {
        return null;
    }

    @Override
    public void setRebootWatchdogUSec(UInt64 uInt64) {

    }

    @Override
    public UInt64 getKExecWatchdogUSec() {
        return null;
    }

    @Override
    public void setKExecWatchdogUSec(UInt64 uInt64) {

    }

    @Override
    public boolean isServiceWatchdogs() {
        return false;
    }

    @Override
    public void setServiceWatchdogs(boolean b) {

    }

    @Override
    public String getControlGroup() {
        return "";
    }

    @Override
    public String getSystemState() {
        return "";
    }

    @Override
    public byte getExitCode() {
        return 0;
    }

    @Override
    public UInt64 getDefaultTimerAccuracyUSec() {
        return null;
    }

    @Override
    public UInt64 getDefaultTimeoutStartUSec() {
        return null;
    }

    @Override
    public UInt64 getDefaultTimeoutStopUSec() {
        return null;
    }

    @Override
    public UInt64 getDefaultTimeoutAbortUSec() {
        return null;
    }

    @Override
    public UInt64 getDefaultDeviceTimeoutUSec() {
        return null;
    }

    @Override
    public UInt64 getDefaultRestartUSec() {
        return null;
    }

    @Override
    public UInt64 getDefaultStartLimitIntervalUSec() {
        return null;
    }

    @Override
    public UInt32 getDefaultStartLimitBurst() {
        return null;
    }

    @Override
    public boolean isDefaultCPUAccounting() {
        return false;
    }

    @Override
    public boolean isDefaultBlockIOAccounting() {
        return false;
    }

    @Override
    public boolean isDefaultIOAccounting() {
        return false;
    }

    @Override
    public boolean isDefaultIPAccounting() {
        return false;
    }

    @Override
    public boolean isDefaultMemoryAccounting() {
        return false;
    }

    @Override
    public boolean isDefaultTasksAccounting() {
        return false;
    }

    @Override
    public UInt64 getDefaultLimitCPU() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitCPUSoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitFSIZE() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitFSIZESoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitDATA() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitDATASoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitSTACK() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitSTACKSoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitCORE() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitCORESoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitRSS() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitRSSSoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitNOFILE() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitNOFILESoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitAS() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitASSoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitNPROC() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitNPROCSoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitMEMLOCK() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitMEMLOCKSoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitLOCKS() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitLOCKSSoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitSIGPENDING() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitSIGPENDINGSoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitMSGQUEUE() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitMSGQUEUESoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitNICE() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitNICESoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitRTPRIO() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitRTPRIOSoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitRTTIME() {
        return null;
    }

    @Override
    public UInt64 getDefaultLimitRTTIMESoft() {
        return null;
    }

    @Override
    public UInt64 getDefaultTasksMax() {
        return null;
    }

    @Override
    public UInt64 getDefaultMemoryPressureThresholdUSec() {
        return null;
    }

    @Override
    public String getDefaultMemoryPressureWatch() {
        return "";
    }

    @Override
    public UInt64 getTimerSlackNSec() {
        return null;
    }

    @Override
    public String getDefaultOOMPolicy() {
        return "";
    }

    @Override
    public int getDefaultOOMScoreAdjust() {
        return 0;
    }

    @Override
    public String getCtrlAltDelBurstAction() {
        return "";
    }

    @Override
    public DBusPath GetUnit(String s) {
        return null;
    }

    @Override
    public DBusPath GetUnitByPID(UInt32 uInt32) {
        return null;
    }

    @Override
    public DBusPath GetUnitByInvocationID(List<Byte> list) {
        return null;
    }

    @Override
    public DBusPath GetUnitByControlGroup(String s) {
        return null;
    }

    @Override
    public GetUnitByPIDFDTuple GetUnitByPIDFD(FileDescriptor fileDescriptor) {
        return null;
    }

    @Override
    public DBusPath LoadUnit(String s) {
        return null;
    }

    @Override
    public DBusPath StartUnitWithFlags(String s, String s1, UInt64 uInt64) {
        return null;
    }

    @Override
    public DBusPath StartUnitReplace(String s, String s1, String s2) {
        return null;
    }

    @Override
    public DBusPath StopUnit(String s, String s1) {
        return null;
    }

    @Override
    public DBusPath ReloadUnit(String s, String s1) {
        return null;
    }

    @Override
    public DBusPath RestartUnit(String s, String s1) {
        return null;
    }

    @Override
    public DBusPath TryRestartUnit(String s, String s1) {
        return null;
    }

    @Override
    public DBusPath ReloadOrRestartUnit(String s, String s1) {
        return null;
    }

    @Override
    public DBusPath ReloadOrTryRestartUnit(String s, String s1) {
        return null;
    }

    @Override
    public EnqueueUnitJobTuple EnqueueUnitJob(String s, String s1, String s2) {
        return null;
    }

    @Override
    public void KillUnit(String s, String s1, int i) {

    }

    @Override
    public void QueueSignalUnit(String s, String s1, int i, int i1) {

    }

    @Override
    public void CleanUnit(String s, List<String> list) {

    }

    @Override
    public void FreezeUnit(String s) {

    }

    @Override
    public void ThawUnit(String s) {

    }

    @Override
    public void ResetFailedUnit(String s) {

    }

    @Override
    public void SetUnitProperties(String s, boolean b, List<SetUnitPropertiesPropertiesStruct> list) {

    }

    @Override
    public void BindMountUnit(String s, String s1, String s2, boolean b, boolean b1) {

    }

    @Override
    public void MountImageUnit(String s, String s1, String s2, boolean b, boolean b1, List<MountImageUnitOptionsStruct> list) {

    }

    @Override
    public void RefUnit(String s) {

    }

    @Override
    public void UnrefUnit(String s) {

    }

    @Override
    public DBusPath StartTransientUnit(String s, String s1, List<StartTransientUnitPropertiesStruct> list, List<StartTransientUnitAuxStruct> list1) {
        return null;
    }

    @Override
    public List<GetUnitProcessesProcessesStruct> GetUnitProcesses(String s) {
        return List.of();
    }

    @Override
    public void AttachProcessesToUnit(String s, String s1, List<UInt32> list) {

    }

    @Override
    public void AbandonScope(String s) {

    }

    @Override
    public DBusPath GetJob(UInt32 uInt32) {
        return null;
    }

    @Override
    public List<GetJobAfterJobsStruct> GetJobAfter(UInt32 uInt32) {
        return List.of();
    }

    @Override
    public List<GetJobBeforeJobsStruct> GetJobBefore(UInt32 uInt32) {
        return List.of();
    }

    @Override
    public void CancelJob(UInt32 uInt32) {

    }

    @Override
    public void ClearJobs() {

    }

    @Override
    public void ResetFailed() {

    }

    @Override
    public void SetShowStatus(String s) {

    }

    @Override
    public List<ListUnitsUnitsStruct> ListUnits() {
        return List.of();
    }

    @Override
    public List<ListUnitsFilteredUnitsStruct> ListUnitsFiltered(List<String> list) {
        return List.of();
    }

    @Override
    public List<ListUnitsByPatternsUnitsStruct> ListUnitsByPatterns(List<String> list, List<String> list1) {
        return List.of();
    }

    @Override
    public List<ListUnitsByNamesUnitsStruct> ListUnitsByNames(List<String> list) {
        return List.of();
    }

    @Override
    public List<ListJobsJobsStruct> ListJobs() {
        return List.of();
    }

    @Override
    public void Subscribe() {

    }

    @Override
    public void Unsubscribe() {

    }

    @Override
    public String Dump() {
        return "";
    }

    @Override
    public String DumpUnitsMatchingPatterns(List<String> list) {
        return "";
    }

    @Override
    public FileDescriptor DumpByFileDescriptor() {
        return null;
    }

    @Override
    public FileDescriptor DumpUnitsMatchingPatternsByFileDescriptor(List<String> list) {
        return null;
    }

    @Override
    public void Reload() {

    }

    @Override
    public void Reexecute() {

    }

    @Override
    public void Exit() {

    }

    @Override
    public void Reboot() {

    }

    @Override
    public void SoftReboot(String s) {

    }

    @Override
    public void PowerOff() {

    }

    @Override
    public void Halt() {

    }

    @Override
    public void KExec() {

    }

    @Override
    public void SwitchRoot(String s, String s1) {

    }

    @Override
    public void SetEnvironment(List<String> list) {

    }

    @Override
    public void UnsetEnvironment(List<String> list) {

    }

    @Override
    public void UnsetAndSetEnvironment(List<String> list, List<String> list1) {

    }

    @Override
    public List<DBusPath> EnqueueMarkedJobs() {
        return List.of();
    }

    @Override
    public List<ListUnitFilesUnitFilesStruct> ListUnitFiles() {
        return List.of();
    }

    @Override
    public List<ListUnitFilesByPatternsUnitFilesStruct> ListUnitFilesByPatterns(List<String> list, List<String> list1) {
        return List.of();
    }

    @Override
    public String GetUnitFileState(String s) {
        return "";
    }

    @Override
    public List<DisableUnitFilesChangesStruct> DisableUnitFiles(List<String> list, boolean b) {
        return List.of();
    }

    @Override
    public EnableUnitFilesWithFlagsTuple EnableUnitFilesWithFlags(List<String> list, UInt64 uInt64) {
        return null;
    }

    @Override
    public List<DisableUnitFilesWithFlagsChangesStruct> DisableUnitFilesWithFlags(List<String> list, UInt64 uInt64) {
        return List.of();
    }

    @Override
    public DisableUnitFilesWithFlagsAndInstallInfoTuple DisableUnitFilesWithFlagsAndInstallInfo(List<String> list, UInt64 uInt64) {
        return null;
    }

    @Override
    public ReenableUnitFilesTuple ReenableUnitFiles(List<String> list, boolean b, boolean b1) {
        return null;
    }

    @Override
    public List<LinkUnitFilesChangesStruct> LinkUnitFiles(List<String> list, boolean b, boolean b1) {
        return List.of();
    }

    @Override
    public PresetUnitFilesTuple PresetUnitFiles(List<String> list, boolean b, boolean b1) {
        return null;
    }

    @Override
    public PresetUnitFilesWithModeTuple PresetUnitFilesWithMode(List<String> list, String s, boolean b, boolean b1) {
        return null;
    }

    @Override
    public List<MaskUnitFilesChangesStruct> MaskUnitFiles(List<String> list, boolean b, boolean b1) {
        return List.of();
    }

    @Override
    public List<UnmaskUnitFilesChangesStruct> UnmaskUnitFiles(List<String> list, boolean b) {
        return List.of();
    }

    @Override
    public List<RevertUnitFilesChangesStruct> RevertUnitFiles(List<String> list) {
        return List.of();
    }

    @Override
    public List<SetDefaultTargetChangesStruct> SetDefaultTarget(String s, boolean b) {
        return List.of();
    }

    @Override
    public String GetDefaultTarget() {
        return "";
    }

    @Override
    public List<PresetAllUnitFilesChangesStruct> PresetAllUnitFiles(String s, boolean b, boolean b1) {
        return List.of();
    }

    @Override
    public List<AddDependencyUnitFilesChangesStruct> AddDependencyUnitFiles(List<String> list, String s, String s1, boolean b, boolean b1) {
        return List.of();
    }

    @Override
    public List<String> GetUnitFileLinks(String s, boolean b) {
        return List.of();
    }

    @Override
    public void SetExitCode(byte b) {

    }

    @Override
    public UInt32 LookupDynamicUserByName(String s) {
        return null;
    }

    @Override
    public String LookupDynamicUserByUID(UInt32 uInt32) {
        return "";
    }

    @Override
    public List<GetDynamicUsersUsersStruct> GetDynamicUsers() {
        return List.of();
    }

    @Override
    public List<DumpUnitFileDescriptorStoreEntriesStruct> DumpUnitFileDescriptorStore(String s) {
        return List.of();
    }
}
