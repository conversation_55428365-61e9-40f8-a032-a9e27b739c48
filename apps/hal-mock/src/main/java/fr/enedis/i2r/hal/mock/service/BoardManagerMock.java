package fr.enedis.i2r.hal.mock.service;

import fr.enedis.hal.BoardManager1;
import fr.enedis.hal.PropertyHWVersionStruct;
import org.freedesktop.dbus.types.UInt64;

public class BoardManagerMock implements BoardManager1 {
    @Override
    public String getADS() {
        return "adsDeTestI2R";
    }

    @Override
    public String getCPLC() {
        return "";
    }

    @Override
    public PropertyHWVersionStruct getHWVersion() {
        return null;
    }

    @Override
    public UInt64 getHWProductionDateTime() {
        return null;
    }

    @Override
    public String getObjectPath() {
        return "/fr/enedis/HAL/BoardManager1";
    }
}
