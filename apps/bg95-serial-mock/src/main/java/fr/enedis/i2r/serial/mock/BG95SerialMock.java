package fr.enedis.i2r.serial.mock;

import java.util.HashMap;

import com.fazecast.jSerialComm.SerialPort;
import com.fazecast.jSerialComm.SerialPortDataListener;
import com.fazecast.jSerialComm.SerialPortEvent;

@SuppressWarnings("unused")
public class BG95SerialMock {

    private static final String portDescriptor = "/dev/pts/1"; // Check README

    private static String currentBand = "8080005";
    private static String currentAPN = "internet";
    private static boolean apnConfigured = false;
    private static boolean isConnectedToServer = false;
    private static boolean pdpContextActive = false;
    private static String serverIP = "";
    private static int serverPort = 0;
    private static boolean dataSent = false;
    private static boolean connectedToPPP = false;
    private static boolean isLTE_M = true;
    private static boolean isNB_IoT = false;
    private static String iotOpMode = "0"; // Default to no NB-IoT optimization
    private static String nwScanSeq = "020301"; // Default LTE-M first
    private static boolean simInserted = true;
    private static boolean pinVerified = false;
    private static String simCCID = "8901234567890123456";
    private static boolean modemOn = true;
    private static String networkOperator = "\"Test Network\"";

    private static StringBuilder commandBuffer = new StringBuilder();

    private static final HashMap<String, String> commandResponses = new HashMap<>();

    static {
        // General AT Commands
        commandResponses.put("AT", "OK");
        commandResponses.put("AT+CSQ", "+CSQ: 15,99\nOK"); // Signal quality
        commandResponses.put("AT+CREG?", "+CREG: 0,1\nOK"); // Network registration
        commandResponses.put("AT+COPS?", "+COPS: 0,0," + networkOperator + "\nOK");
        commandResponses.put("AT+CGATT?", "+CGATT: 1\nOK"); // GPRS attach
        commandResponses.put("AT+CGSN", "353456789012345\nOK"); // IMEI
        commandResponses.put("AT+CIMI", "208930123456789\nOK"); // IMSI
        commandResponses.put("AT+QCCID", simCCID + "\nOK");
        commandResponses.put("AT+CFUN?", "+CFUN: 1\nOK"); // Default to full functionality

        // APN and Data Connection
        commandResponses.put("AT+CGDCONT?", getAPNInfo());
        commandResponses.put("AT+QIACT?", "+QIACT: 1,1,\"" + currentAPN + "\",\"********\"\nOK");

        commandResponses.put("AT+CPSI?", getCurrentCellInfo());

        // Default responses
        commandResponses.put("ERROR", "ERROR");
    }

    public static void main(String[] args) {
        // Set up the serial port
        SerialPort serialPort = SerialPort.getCommPort(portDescriptor);
        serialPort.setBaudRate(115200);

        if (!serialPort.openPort()) {
            System.out.println("Failed to open the port!");
            return;
        }

        System.out.println("Quectel BG95-M5 Simulator started...");
        serialPort.addDataListener(new SerialPortDataListener() {
            @Override
            public int getListeningEvents() {
                return SerialPort.LISTENING_EVENT_DATA_AVAILABLE;
            }

            @Override
            public void serialEvent(SerialPortEvent event) {
                if (event.getEventType() != SerialPort.LISTENING_EVENT_DATA_AVAILABLE) {
                    return;
                }

                byte[] newData = new byte[serialPort.bytesAvailable()];
                serialPort.readBytes(newData, newData.length);

                // Append received data to buffer
                for (byte b : newData) {
                    char c = (char) b;
                    commandBuffer.append(c);

                    // Check for line termination
                    if (c == '\n' || c == '\r') {
                        String fullCommand = commandBuffer.toString().trim();
                        commandBuffer.setLength(0); // Clear the buffer

                        if (!fullCommand.isEmpty()) {
                            System.out.println("Received command: " + fullCommand);

                            // Process the full command
                            String response = handleCommand(fullCommand);
                            sendResponse(serialPort, response);
                        }
                    }
                }
            }
        });
    }

    private static String handleCommand(String command) {
        if (command.startsWith("AT+CGDCONT=")) {
            // Configure APN
            String[] parts = command.split(",");
            if (parts.length >= 3) {
                currentAPN = parts[2].replace("\"", "");
                apnConfigured = true;
                return "OK";
            }
            return "ERROR";
        } else if (command.startsWith("AT+QCFG=\"band\"")) {
            if (command.equals("AT+QCFG=\"band\",F,0,0")) {
                // Reset band configuration
                currentBand = "default";
                return "OK";
            }
            // Configure band
            String[] parts = command.split(",");
            if (parts.length >= 3) {
                currentBand = parts[2].replace("\"", "");
                return "OK";
            }
            return "ERROR";
        } else if (command.startsWith("AT+QCFG=\"iotopmode\"")) {
            // Configure IoT optimization mode
            String[] parts = command.split(",");
            if (parts.length == 2) {
                iotOpMode = parts[1];
                return "OK";
            }
            return "ERROR";
        } else if (command.startsWith("AT+QCFG=\"nwscanseq\"")) {
            // Configure network scan sequence
            String[] parts = command.split(",");
            if (parts.length == 2) {
                nwScanSeq = parts[1];
                return "OK";
            }
            return "ERROR";
        } else if (command.startsWith("AT+QCFG=\"simeffect\"")) {
            // Simulate SIM effect (no operation in this simulator)
            return "OK";
        } else if (command.startsWith("AT+CPIN=")) {
            // SIM PIN verification
            if (command.equals("AT+CPIN=1234")) {
                pinVerified = true;
                return "OK";
            }
            return "ERROR";
        } else if (command.equals("AT+CFUN=1")) {
            // Full functionality mode
            modemOn = true;
            return "OK";
        } else if (command.equals("AT+CFUN=4")) {
            // Airplane mode
            modemOn = false;
            return "OK";
        } else if (command.startsWith("AT+QICSGP=")) {
            // Configure PDP context
            if (command.contains(currentAPN)) {
                apnConfigured = true;
                return "OK";
            }
            return "ERROR";
        } else if (command.equals("AT+QIACT=1")) {
            // Activate PDP context
            if (apnConfigured) {
                pdpContextActive = true;
                return "+QIACT: 1,1,\"" + currentAPN + "\",\"********\"\nOK";
            }
            return "ERROR";
        } else if (command.startsWith("AT+QPING=1,")) {
            // Simulate ping
            return "+QPING: 1,\"*******\",32,64,100ms\nOK";
        } else if (command.startsWith("AT+QIOPEN=1,0")) {
            // Open a TCP connection
            String[] parts = command.split(",");
            if (parts.length >= 5) {
                serverIP = parts[3].replace("\"", "");
                serverPort = Integer.parseInt(parts[4]);
                isConnectedToServer = true;
                return "+QIOPEN: 0,0";
            }
            return "+QIOPEN: 0,1"; // Connection failed
        } else if (command.equals("AT+QISEND=0,10")) {
            // Simulate sending data
            if (isConnectedToServer) {
                dataSent = true;
                return ">"; // Prompt for data
            }
            return "ERROR";
        } else if (command.equals("AT+QICLOSE=0")) {
            // Close the TCP connection
            if (isConnectedToServer) {
                isConnectedToServer = false;
                return "OK";
            }
            return "ERROR";
        } else if (command.equals("AT+QIDEACT=1")) {
            // Deactivate PDP context
            if (pdpContextActive) {
                pdpContextActive = false;
                return "OK";
            }
            return "ERROR";
        }

        // Default to predefined responses
        return commandResponses.getOrDefault(command, "ERROR");
    }

    private static String getAPNInfo() {
        if (apnConfigured) {
            return "+CGDCONT: 1,\"IP\",\"" + currentAPN + "\"\nOK";
        }
        return "+CGDCONT: 1,\"IP\",\"\"\nOK";
    }

    private static String getCurrentCellInfo() {
        if (isLTE_M) {
            return "+CPSI: LTE-M,\"Online\",123,\"234567\",\"01FF\",\"10\"";
        } else if (isNB_IoT) {
            return "+CPSI: NB-IoT,\"Online\",123,\"234567\",\"01FF\",\"10\"";
        }
        return "+CPSI: \"No Service\"";
    }

    private static void sendResponse(SerialPort serialPort, String response) {
        String formattedResponse = response + "\r\n";
        serialPort.writeBytes(formattedResponse.getBytes(), formattedResponse.length());
        System.out.println("Sent: " + formattedResponse.trim());
    }
}
