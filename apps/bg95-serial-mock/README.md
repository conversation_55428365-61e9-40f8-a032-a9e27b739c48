# BG95 Serial Mock

## Installation
Tout d'abord il faut installer les packages dont on aura besoin. Socat pour simuler qu'un device est branché sur un port Serial et Minicom pour envoyer des commandes AT.

Dans le devcontainer, ces dépendances sont déjà installées.

```
sudo apt update
sudo apt install socat minicom
```

## Configuration

### Socat
Pour simuler le rattachement physique du *virtual device*, exécuter dans un terminal séparé :

`socat -d -d pty,raw,echo=0 pty,raw,echo=0`

En sortie ça va afficher les ports créés. Par exemple :

```
/dev/pts/1
/dev/pts/2
```

NB : La valeur des ports peut être différentes

Le premier port est à utiliser sur le code Java du simulateur. C'est à dire la variable `portDescriptor`.

La deuxième est à utiliser pour envoyer les commandes AT soit à travers **minicom** soit à travers la commande **echo** comme suit :

`echo "AT" > /dev/pts/2`

### Minicom
Si vous souhaitez utiliser **minicom**, il faut le configurer comme suit :

`sudo minicom -s`

Puis choisir la configuration suivante :

1. Aller sur **Serial port setup**.
2. Modifier **Serial Device** pour pointer sur le bon port, par exemple **/dev/pts/2**
3. Modifier **baud rate** à **115200**
4. Modifier **Hardware Flow Control** ***(F)*** à **No**.
5. Retourner sur le menu principal et choisir **Screen**
6. Activer **Local Echo** ***(Q)*** et **Line Wrap** ***(R)***
7. Sauvegarder la configuration en choisissant **Save setup as dfl**
8. Fermer **minicom** et le lancer de nouveau avec la commande `minicom -l`

## Test

Pour tester, il faut :
1. Démarrer **socat** et suivre les étapes décrites précédemment
2. Démarrer la classe **BG95SerialMock**
3. Démarrer **minicom** et y lancer les commandes AT

Dans le cas d'un vrai connexion au modem, on n'aurait pas besoin de **socat**, et le port sera plutôt `/dev/ttyUSB0` pour la RevPi/Linux ou `COM3` pour Windows/WSL.

### Commandes AT

L'ensemble des commandes AT qu'on peut exécuter pour tester :
```
AT
AT+CSQ
AT+CREG?
AT+COPS?
AT+CGSN
AT+CIMI
AT+QCCID
AT+CFUN?
AT+CFUN=1
AT+CFUN=4
AT+CGDCONT=1,"IP","$APN"
AT+CGDCONT?
AT+QICSGP=1,3,"$APN","$login","$password",2
AT+QIACT=1
AT+QIACT?
AT+QIDEACT=1
AT+QCFG="band",F,0,0
AT+QCFG="band",3,8080005,8080005
AT+QCFG="iotopmode",0
AT+QCFG="nwscanseq",020301
AT+QCFG="simeffect",0
AT+CPIN=1234
AT+CPSI?
AT+QPING=1,"*******"
AT+QIOPEN=1,0,"TCP","$server_ip",$port,0,1
AT+QISEND=0,10
AT+QICLOSE=0
```

En sortie de la commande est le résultat de l'exécution.

NB : Si les commandes AT qui nécessitent préalablement la configuration de l'APN sont exécutés, on aura `ERROR` en retour, comme dans un vrai modem.

### Troubleshooting

Au besoin si **minicom** ne fonctionne toujours pas
- Vérifier les persmissions du port d'accès

`sudo chmod a+rw /dev/ttyUSB0`

- Ajouter l'utilisateur au dialout group pour un accès permanent

`sudo usermod -aG dialout $USER`
