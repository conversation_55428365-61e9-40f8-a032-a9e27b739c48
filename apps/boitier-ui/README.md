# Boitier-UI

Web app to show the state of LEDs of Boitier AO3 when i2R app is running.

## How To

1. On this directory (apps/boitier-ui), run the following command to launch the UI Server:

```bash
deno task start
```

2. On your browser, open the following URL: http://localhost:3000

3. **Start DBus mock and the i2R main app.** This will generate the necessary logs.

## Logs

### Path

    /var/log/i2r/leds.log

### Format

Separator: 20 dashes

    --------------------
    DATE TIME
    LED $LED_NAME $STATUS $COLOR
    ...

### Values

    $LED_NAME: POWER, CPT, ETH1, ETH2, SI, RSL
    $STATUS: ON, OFF, BLINK, SLOW_BLINK, FAST_BLINK
    $COLOR: GREEN, RED, YELLOW

### Example

    --------------------
    2024-09-20 10:15:00
    LED POWER ON GREEN
    LED CPT SLOW_BLINK YELLOW
    LED ETH1 BLINK YELLOW
    LED ETH2 ON RED
    LED SI BLINK GREEN
    LED RSL FAST_BLINK RED
