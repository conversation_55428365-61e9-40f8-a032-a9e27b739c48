import express from 'express';
import cors from 'cors';
import {readFile} from 'node:fs';

const ledsLogPath = '/var/log/i2r/leds.log';
const appLogPath = '/var/log/i2r/app.log';
const dbusLogPath = '/var/log/i2r/hal_mock.log';

const app = express();
app.use(cors()); // Enable CORS

const readLogLastSection = async (filePath) => {
    return new Promise((resolve, reject) => {
        readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                return reject(err);
            }
            const separator = '--------------------'
            const sections = data.split(separator).map(section => section.trim()).filter(section => section);
            const lastSection = sections[sections.length - 1];
            resolve(lastSection);
        });
    });
};

const readLog = async (filePath) => {
    return new Promise((resolve, reject) => {
        readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                return reject(err);
            }
            resolve(data);
        });
    });
};

app.get('/leds', async (req, res) => {
    try {
        const currentLedsState = await readLogLastSection(ledsLogPath);
        res.send(currentLedsState);
    } catch (err) {
        const errorMsg = 'Error reading LEDs log file';
        console.error(`${errorMsg}: ${err}`);
        res.status(500).send(errorMsg);
    }
});

app.get('/app', async (req, res) => {
    try {
        const appLog = await readLog(appLogPath);
        res.send(appLog);
    } catch (err) {
        const errorMsg = 'Error reading APP log file';
        console.error(`${errorMsg}: ${err}`);
        res.status(500).send(errorMsg);
    }
});

app.get('/dbus', async (req, res) => {
    try {
        const dbusLog = await readLog(dbusLogPath);
        res.send(dbusLog);
    } catch (err) {
        const errorMsg = 'Error reading DBUS log file';
        console.error(`${errorMsg}: ${err}`);
        res.status(500).send(errorMsg);
    }
});

app.use(express.static('public'))

// Start the server
const PORT = 3000;
app.listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
});
