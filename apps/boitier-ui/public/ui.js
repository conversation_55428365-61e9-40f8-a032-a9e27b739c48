function updateLedStatus(ledId, status, color) {
    const ledElement = $('#' + ledId + ' .led-circle');
    ledElement.removeClass('on off blink slow-blink fast-blink red green yellow');

    if (color) {
        ledElement.addClass(color.toLowerCase());
    }

    switch (status) {
        case 'ON':
            // ledElement.addClass('on');
            ledElement.removeClass('blink slow-blink fast-blink');
            break;
        case 'OFF':
            ledElement.addClass('off');
            break;
        case 'BLINK':
            ledElement.addClass('blink');
            break;
        case 'SLOW_BLINK':
            ledElement.addClass('slow-blink');
            break;
        case 'FAST_BLINK':
            ledElement.addClass('fast-blink');
            break;
        default:
            ledElement.addClass('off');
            break;
    }
}

function updateTerminalContent(logId, logContent) {
    const lineBreak = /\r\n|\r|\n/g;
    const logElement = $('#' + logId);
    const currentLogContent = logElement.text().trim();
    const newLogContent = logContent.trim().replace(lineBreak, '');

    // If there is new content, update logs and scroll to the bottom
    if (currentLogContent !== newLogContent) {
        const htmlContent = logContent.replace(lineBreak, '<br>');
        logElement.html(htmlContent);
        const terminalContent = logElement.closest('.terminal-content');
        terminalContent.scrollTop(terminalContent.prop("scrollHeight"));
    }
}


function fetchLedsLog() {
    $.ajax({
        url: '/leds',
        method: 'GET',
        success: function (ledsLogData) {
            const lines = ledsLogData.trim().split('\n');
            lines.forEach(line => {
                if (line.includes('LED')) {
                    const parts = line.trim().split(' ');
                    const ledName = parts[1].toLowerCase();
                    const status = parts[2];
                    const color = parts[3] ? parts[3] : null;

                    updateLedStatus(`${ledName}-led`, status, color);
                }
            });
        },
        error: function () {
            console.error('Error fetching LEDs log data');
        }
    });
}

function fetchAppLog() {
    $.ajax({
        url: '/app',
        method: 'GET',
        success: function (appLogData) {
            updateTerminalContent('app', appLogData);
        },
        error: function () {
            console.error('Error fetching APP log data');
        }
    });
}

function fetchDBusLog() {
    $.ajax({
        url: '/dbus',
        method: 'GET',
        success: function (dbusLogData) {
            updateTerminalContent('dbus', dbusLogData);
        },
        error: function () {
            console.error('Error fetching DBUS log data');
        }
    });
}

function updateUI() {
    fetchLedsLog();
    fetchAppLog();
    fetchDBusLog();
}

// Initial fetch
updateUI();
// Periodically fetch logs
setInterval(updateUI, 100);

$(document).ready(function () {
    let toggleLeft = false;
    let toggleRight = false;

    $('#toggle-left-terminal').click(function () {
        if (toggleLeft) {
            $('#left-terminal').addClass("hidden")
            toggleLeft = false;
        } else {
            $('#left-terminal').removeClass("hidden")
            toggleLeft = true;
        }
    });
    $('#toggle-right-terminal').click(function () {
        if (toggleRight) {
            $('#right-terminal').addClass("hidden")
            toggleRight = false;
        } else {
            $('#right-terminal').removeClass("hidden")
            toggleRight = true;
        }
    });
});

