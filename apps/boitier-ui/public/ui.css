body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f4f4f4;
}

.grid-container {
    display: grid;
    grid-template-columns: 41% 16% 41%;
    grid-gap: 20px;
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
    height: 100vh;
}

.terminal-block {
    background-color: black;
    color: #66FF66;
    font-family: 'Consolas', monospace;
    border: 2px solid black;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.terminal-header {
    background-color: #66FF66;
    color: black;
    padding: 10px;
    text-align: center;
}

.terminal-content {
    padding: 10px;
    flex-grow: 1;
    overflow-y: auto;
}

.hidden {
    visibility: hidden;
}

#boitier-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    padding: 20px;
}

#leds-container {
    background-color: #f9f9f9;
    border: 2px solid #ddd;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    width: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
}

h1 {
    font-size: 24px;
    color: #333;
    margin-bottom: 20px;
}

.led {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 10px;
}

.led span {
    min-width: 50px;
    text-align: right;
}

.led-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
}

.led-circle.off {
    background-color: gray;
}


.led-circle.on {
    background-color: green;
}

.led-circle.blink {
    animation: blink 1s infinite;
}

.led-circle.slow-blink {
    animation: slow-blink 2s infinite;
}

.led-circle.fast-blink {
    animation: fast-blink 0.5s infinite;
}

.led-circle.red {
    background-color: red;
}

.led-circle.green {
    background-color: green;
}

.led-circle.yellow {
    background-color: #FFEB3B;
}

@keyframes blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

@keyframes slow-blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

@keyframes fast-blink {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0;
    }
}

#logo {
    text-align: center;
    margin-top: 20px;
}

#logo img {
    max-width: 100px;
    display: block;
    margin: 0 auto;
}

#toggle-buttons-container {
    position: absolute;
    bottom: 20px;
    display: flex;
    gap: 10px;
}

.toggle-terminal-button {
    background-color: black;
    color: #66FF66;
    border: 2px solid #66FF66;
    font-family: 'Consolas', monospace;
    cursor: pointer;
    height: 40px;
    width: 100px;
}

@media (max-width: 992px) {
    .grid-container {
        grid-template-columns: 1fr;
        grid-gap: 10px;
        height: auto;
    }

    #led-container, .terminal-block {
        height: auto;
    }

    #toggle-buttons-container {
        position: static;
        margin-top: 20px;
    }

    .toggle-terminal-button {
        width: 100px;
    }
}
