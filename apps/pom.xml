<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>fr.enedis.i2r</groupId>
        <artifactId>parent</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>apps</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>hal-mock</module>
        <module>hal-mock-config</module>
        <module>bg95-serial-mock</module>
        <module>si-mock</module>
        <module>main</module>
    </modules>
    <build>
        <plugins>
            <!-- Disable Code Coverage un POM project -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.12</version>
                <executions>
                    <execution>
                        <phase>none</phase>
                        <id>coverage-initialize</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                        <inherited>false</inherited>
                    </execution>
                    <execution>
                        <id>coverage-report</id>
                        <phase>none</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <inherited>false</inherited>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
