package fr.enedis.i2r.hal.mock.config;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Objects;

public class ConfigWriter {

    private static final String PLACEHOLDER = "HAL_SERVICE_NAME";
    private static final String SERVICE_TEMPLATE_FILENAME = PLACEHOLDER + ".service";
    private static final String CONF_TEMPLATE_FILENAME = PLACEHOLDER + ".conf";

    public static void configureService(String halServiceName, String outputDirectory) {
        // Configure .service
        writeToFile(
                outputDirectory + "/dbus/service",
                SERVICE_TEMPLATE_FILENAME.replace(PLACEHOLDER, halServiceName),
                readFromFile(SERVICE_TEMPLATE_FILENAME).replace(PLACEHOLDER, halServiceName));

        // Configure .conf
        writeToFile(
                outputDirectory + "/dbus/conf",
                CONF_TEMPLATE_FILENAME.replace(PLACEHOLDER, halServiceName),
                readFromFile(CONF_TEMPLATE_FILENAME).replace(PLACEHOLDER, halServiceName));
    }

    private static String readFromFile(String resourceFileName) {
        String result = null;
        try {
            String resourcePath = Objects.requireNonNull(
                    ConfigWriter.class.getClassLoader().getResource(resourceFileName),
                    "File " + resourceFileName + " not found in resources").getPath();
            result = new String(Files.readAllBytes(Paths.get(resourcePath)));
        } catch (IOException e) {
            throw new IllegalArgumentException("Error reading file " + resourceFileName, e);
        }
        return result;
    }

    private static void writeToFile(String directory, String filename, String content) {
        try {
            File dir = new File(directory);
            if (!dir.exists()) {
                dir.mkdirs();
            }
            File file = new File(dir, filename);
            Files.write(Paths.get(file.toURI()), content.getBytes());
            System.out.println("File written to: " + file.getAbsolutePath());
        } catch (IOException e) {
            throw new IllegalArgumentException("Error writing to file " + filename, e);
        }
    }
}
