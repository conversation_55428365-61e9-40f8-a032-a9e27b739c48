package fr.enedis.i2r.hal.mock.config;

public class Main {

    public static void main(String[] args) {
        String outputDirectory = "/tmp/i2r";
        if (args.length != 0) {
            outputDirectory = args[0];
        }

        ConfigWriter.configureService("fr.enedis.HAL.LEDManager1", outputDirectory);
        ConfigWriter.configureService("fr.enedis.HAL.ModemManager1", outputDirectory);
    }
}
