# README

Ce projet a pour but de générer les fichiers de configuration de DBUS. Fichier de configuration notamment utiliser par le projet "hal-mock"

## Fonctionnement

Si vous lancez ce programme sans paramètre, il générera dans le dossier /tmp/i2r/dbus les fichiers ``*.conf`` et
``*.service`` de nos services DBus.

* Il faut ensuite déplacer ces fichiers dans leur repertoire respectif, à savoir :
    * /etc/dbus-1/system.d/ pour les fichiers *.conf
    * /usr/share/dbus-1/system-services/ pour les fichiers *.services
* S'assurer du propriétaire des fichiers
* Et redémarrer le service dbus.

````shell
sudo mv /tmp/i2r/dbus/conf/* /etc/dbus-1/system.d/
sudo chown wudi:wudi /etc/dbus-1/system.d/*
sudo mv /tmp/i2r/dbus/service/* /usr/share/dbus-1/system-services/
sudo chown wudi:wudi /usr/share/dbus-1/system-services/*
sudo service dbus restart
````
