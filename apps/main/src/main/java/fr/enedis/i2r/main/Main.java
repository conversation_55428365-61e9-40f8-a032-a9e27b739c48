package fr.enedis.i2r.main;

import java.sql.SQLException;
import java.time.Clock;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.net.ssl.SSLContext;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import fr.enedis.i2r.comsi.ComSI;
import fr.enedis.i2r.comsi.ComSiConfiguration;
import fr.enedis.i2r.comsi.ports.BoardManagerPort;
import fr.enedis.i2r.comsi.ports.DatabaseUpdateWatcherPort;
import fr.enedis.i2r.comsi.ports.ModuleSecuritePort;
import fr.enedis.i2r.comsi.ports.SleeperPort;
import fr.enedis.i2r.comsi.ports.si.SiClientPort;
import fr.enedis.i2r.comsi.rest.HttpServer;
import fr.enedis.i2r.comsi.status.BipStatusManager;
import fr.enedis.i2r.infra.DummyModuleSecuriteAdapter;
import fr.enedis.i2r.infra.ThreadSleeperAdapter;
import fr.enedis.i2r.infra.hal.DbusBoardManagerAdapter;
import fr.enedis.i2r.infra.hal.DbusLedAdapter;
import fr.enedis.i2r.infra.hal.DbusModemBG95Adapter;
import fr.enedis.i2r.infra.params.DbUpdateWatcherAdapter;
import fr.enedis.i2r.infra.params.SqliteParamsProvider;
import fr.enedis.i2r.infra.params.errors.RequiredParameterMissingException;
import fr.enedis.i2r.infra.rest.HttpClient;
import fr.enedis.i2r.infra.rest.SiClientHttpAdapter;
import fr.enedis.i2r.infra.shell.ShellExecutorAdapter;
import fr.enedis.i2r.infra.systemd.SystemdAdapter;
import fr.enedis.i2r.security.SecurityModule;
import fr.enedis.i2r.system.LoggingLoader;
import fr.enedis.i2r.system.SystemConfiguration;
import fr.enedis.i2r.system.SystemModule;
import fr.enedis.i2r.system.ports.LedPort;
import fr.enedis.i2r.system.ports.ShellExecutorPort;
import fr.enedis.i2r.system.ports.SystemdPort;

public class Main {
    private static final Duration INTERVAL_BETWEEN_TWO_COMSI_CONFIGURATION_FETCHS = Duration.ofSeconds(10);
    private static final String DATABASE_FILE_PATH = "/var/lib/i2r/i2r-params.db";
    private static final String LOGGER_NAME = "ROOT";

    private static final Logger logger = LoggerFactory.getLogger(Main.class);

    public static void main(String[] args) {
        try {
            logger.info("Démarrage de i2R");
            System.out.println(" ░░ ░░░░░░  ░░░░░░  ");
            System.out.println(" ▒▒      ▒▒ ▒▒   ▒▒ ");
            System.out.println(" ▒▒  ▒▒▒▒▒  ▒▒▒▒▒▒  ");
            System.out.println(" ▓▓ ▓▓      ▓▓   ▓▓ ");
            System.out.println(" ██ ███████ ██   ██ ");

            ThreadSleeperAdapter threadSleeper = new ThreadSleeperAdapter();
            SqliteParamsProvider sqliteParamsProvider = new SqliteParamsProvider(DATABASE_FILE_PATH);

            logger.info("Récupération de la configuration comsi...");
            ComSiConfiguration comSiConfiguration = retryGetComSiConfiguration(sqliteParamsProvider, threadSleeper);
            SystemConfiguration systemConfiguration = sqliteParamsProvider.getSystemConfiguration();
            logger.info("Configuration ComSI obtenue");

            // Services
            LedPort ledPort = new DbusLedAdapter();
            DbusModemBG95Adapter modemPort = DbusModemBG95Adapter.connect();
            BoardManagerPort boardManagerPort = new DbusBoardManagerAdapter();

            SecurityModule securityModule = new SecurityModule();

            SystemdPort systemdPort = new SystemdAdapter();
            ShellExecutorPort shellExecutor = new ShellExecutorAdapter();

            SSLContext sslContext = securityModule.getSslContext();
            SiClientPort siClient = new SiClientHttpAdapter(comSiConfiguration, new HttpClient(sslContext));
            ModuleSecuritePort moduleSecuritePort = new DummyModuleSecuriteAdapter(boardManagerPort);

            DatabaseUpdateWatcherPort dbUpdateWatcherPort = new DbUpdateWatcherAdapter(sqliteParamsProvider, DATABASE_FILE_PATH);

            var system = new SystemModule(systemConfiguration, ledPort, systemdPort);

            var comSI = new ComSI(
                    comSiConfiguration,
                    sqliteParamsProvider,
                    siClient,
                    boardManagerPort,
                    moduleSecuritePort,
                    modemPort,
                    dbUpdateWatcherPort,
                    Clock.systemDefaultZone());

            var bipStatusManager = new BipStatusManager(sqliteParamsProvider, boardManagerPort, system);

            // REST APIs
            var loggingLoader = new LoggingLoader(sqliteParamsProvider);
            loggingLoader.reloadLogger(LOGGER_NAME, systemConfiguration.logLevel());

            var httpServer = new HttpServer(bipStatusManager, comSiConfiguration, shellExecutor,
                    loggingLoader);

            Runtime.getRuntime().addShutdownHook(new Thread(() -> logger.info("i2R s'arrête.")));
            ExecutorService executorService = Executors.newFixedThreadPool(3);
            executorService.submit(system);
            executorService.submit(comSI);
            executorService.submit(httpServer);

            logger.info("i2R started and is running...");
            while (true) {
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            logger.error("Error while starting i2R: {}", e.getMessage());
            System.exit(1);
        }
    }

    /**
     * Continue d'essayer de récupérer la configuration de comsi jusqu'à ce qu'elle
     * soit complete
     *<p>
     * Ici, nous devons attendre que le datacenter primaire soit renseigné avant de
     * démarrer comsi
     *
     * @return la configuration complete de comsi
     * @throws InterruptedException lorsque le programme est interrompu lors d'un
     *                              sleep
     * @throws SQLException         lors du rafraîchissement des données
     */
    private static ComSiConfiguration retryGetComSiConfiguration(SqliteParamsProvider provider,
            SleeperPort sleeper) throws InterruptedException, SQLException {
        while (true) {
            try {
                return provider.getComSiConfiguration();
            } catch (RequiredParameterMissingException e) {
                logger.warn("la configuration requise pour comsi est manquante");
            } catch (Exception e) {
                logger.error("configuration de comsi invalide");
            }
            sleeper.sleep(INTERVAL_BETWEEN_TWO_COMSI_CONFIGURATION_FETCHS);
            provider.refreshConfiguration();
        }
    }
}
